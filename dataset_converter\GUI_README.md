# 数据集转换器 GUI 版本

基于PySide6的可视化数据集转换工具，为原有的命令行数据集转换器提供了直观易用的图形界面。

## 🚀 功能特性

### 支持的转换格式
- **COCO** ↔ **YOLO** (检测、分割、关键点)
- **COCO** ↔ **Pascal VOC** (检测)
- **YOLO** ↔ **Pascal VOC** (检测)
- **LabelMe** ↔ **COCO** (检测、分割)
- **LabelMe** ↔ **YOLO** (检测)
- **ImageNet** ↔ **YOLO分类**

### 界面功能
- 🎯 **直观的转换类型选择** - 下拉菜单选择转换类型
- ⚙️ **智能参数配置** - 根据转换类型自动显示相关参数
- 📁 **便捷的文件选择** - 图形化文件/目录选择器
- 📊 **实时进度显示** - 转换进度条和详细日志
- 👀 **数据集预览** - 查看数据集基本信息、类别和样本
- 💾 **配置保存/加载** - 保存常用配置以便重复使用
- 🔄 **批量处理支持** - 支持批量转换多个数据集（开发中）

## 📦 安装要求

### 系统要求
- Python 3.9 或更高版本
- Windows 10/11, macOS 10.14+, 或 Linux

### 依赖包
```
PySide6>=6.9.1
opencv-python>=4.5.0
Pillow>=8.0.0
numpy>=1.19.0
pycocotools>=2.0.2
lxml>=4.6.0
tqdm>=4.60.0
matplotlib>=3.3.0
scikit-image>=0.18.0
PyYAML>=5.4.0
```

## 🛠️ 安装和启动

### 1. 安装依赖
```bash
# 使用pip安装
pip install -r requirements.txt

# 或使用conda
conda env create -f environment.yml
conda activate dataset-converter
```

### 2. 测试安装
```bash
python test_gui.py
```

### 3. 启动GUI

#### Windows
```cmd
# 双击运行
start_gui.bat

# 或命令行
python gui_main.py
```

#### Linux/macOS
```bash
# 给脚本执行权限
chmod +x start_gui.sh
./start_gui.sh

# 或直接运行
python3 gui_main.py
```

## 📁 项目结构

```
dataset_converter/
├── gui/                          # GUI模块
│   ├── __init__.py              # 包初始化
│   ├── main_window.py           # 主窗口
│   ├── conversion_widget.py     # 转换配置组件
│   ├── file_selector.py         # 文件选择组件
│   ├── progress_widget.py       # 进度显示组件
│   ├── preview_widget.py        # 数据集预览组件
│   └── utils.py                 # GUI工具函数
├── gui_main.py                  # GUI应用程序入口
├── start_gui.bat               # Windows启动脚本
├── start_gui.sh                # Linux/macOS启动脚本
├── test_gui.py                 # GUI测试脚本
├── GUI使用说明.md               # 详细使用说明
├── GUI_README.md               # 本文件
└── requirements.txt            # 依赖列表（已更新）
```

## 🎯 使用方法

### 基本流程
1. **选择转换类型** - 从下拉菜单选择需要的转换（如"COCO → YOLO"）
2. **配置参数** - 填写输入文件/目录、输出目录等参数
3. **预览数据集** - 在右侧查看数据集信息（可选）
4. **开始转换** - 点击"开始转换"按钮
5. **查看结果** - 在进度区域查看转换状态和日志

### 参数类型说明
- **文件选择** - 点击"浏览..."选择单个文件
- **目录选择** - 点击"浏览..."选择文件夹
- **下拉选择** - 从预定义选项中选择
- **复选框** - 启用/禁用功能选项
- **文本输入** - 手动输入文本或数值

### 配置管理
- **保存配置** - 文件 → 保存配置 (Ctrl+S)
- **加载配置** - 文件 → 打开配置 (Ctrl+O)
- **新建配置** - 文件 → 新建配置 (Ctrl+N)

## 🔧 高级功能

### 数据集预览
右侧预览区域提供三个选项卡：
- **基本信息** - 数据集类型、图片数量、标注数量等
- **类别信息** - 所有类别的详细列表
- **样本预览** - 部分样本文件的信息

### 进度监控
- **实时进度条** - 显示转换进度百分比
- **详细日志** - 显示转换过程中的详细信息
- **状态指示** - 准备就绪/转换中/转换完成/转换失败
- **日志管理** - 清空日志、保存日志到文件

### 错误处理
- **参数验证** - 自动检查必填参数和文件路径
- **错误提示** - 友好的错误消息和解决建议
- **异常恢复** - 转换失败后可以重新配置和重试

## 🎨 界面特性

### 响应式布局
- 可调整的分割器布局
- 自适应窗口大小
- 支持高DPI显示

### 用户体验
- 直观的图标和按钮
- 快捷键支持
- 状态栏信息提示
- 工具提示说明

### 主题支持
- 默认系统主题
- 可选深色主题（在代码中启用）
- 跨平台一致的外观

## 🐛 故障排除

### 常见问题

#### 启动失败
```bash
# 检查Python版本
python --version

# 重新安装依赖
pip install -r requirements.txt

# 测试GUI组件
python test_gui.py
```

#### 转换失败
- 检查输入文件路径是否正确
- 确认数据集格式符合要求
- 查看错误日志了解具体问题
- 尝试使用命令行版本验证

#### 界面显示问题
- 更新PySide6到最新版本
- 检查系统图形驱动
- 尝试不同的系统主题

### 获取帮助
1. 查看 `GUI使用说明.md` 获取详细说明
2. 运行 `test_gui.py` 检查环境配置
3. 查看应用程序内的帮助菜单
4. 检查转换日志中的错误信息

## 🔄 与命令行版本的关系

GUI版本是对原有命令行转换器的封装，具有以下特点：

### 优势
- **易用性** - 图形界面更直观，无需记忆命令行参数
- **可视化** - 提供数据集预览和转换进度显示
- **配置管理** - 可以保存和重用转换配置
- **错误处理** - 更友好的错误提示和处理

### 兼容性
- **完全兼容** - 使用相同的转换核心代码
- **参数映射** - GUI参数直接映射到命令行参数
- **功能对等** - 支持所有命令行版本的转换功能

### 选择建议
- **GUI版本** - 适合日常使用、学习和演示
- **命令行版本** - 适合批量处理、脚本自动化和服务器环境

## 🚧 开发计划

### 即将推出
- [ ] 批量转换功能
- [ ] 数据集验证工具
- [ ] 更多主题选项
- [ ] 转换历史记录
- [ ] 插件系统支持

### 长期规划
- [ ] 云端转换支持
- [ ] 数据集统计分析
- [ ] 自定义转换规则
- [ ] 多语言界面支持

## 📄 许可证

本项目遵循与原数据集转换器相同的开源许可证。

## 🤝 贡献

欢迎提交Issue和Pull Request来改进GUI界面！

### 贡献指南
1. Fork项目
2. 创建功能分支
3. 提交更改
4. 创建Pull Request

---

**享受使用数据集转换器GUI版本！** 🎉
