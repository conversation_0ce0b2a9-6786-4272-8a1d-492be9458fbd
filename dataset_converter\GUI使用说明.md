# 数据集转换器 GUI 使用说明

## 概述

数据集转换器GUI版本提供了一个直观易用的图形界面，支持多种计算机视觉数据集格式之间的转换。

## 功能特性

### 支持的数据集格式
- **COCO** (JSON格式) - Microsoft通用对象上下文数据集
- **YOLO** (TXT格式) - You Only Look Once
- **Pascal VOC** (XML格式) - 视觉对象类别数据集
- **LabelMe** (JSON格式) - 图像标注工具格式
- **ImageNet** (目录结构) - 图像分类数据集

### 支持的任务类型
- **目标检测** - 边界框检测
- **实例分割** - 像素级对象分割
- **关键点检测** - 人体姿态估计
- **图像分类** - 单/多类别分类

### 支持的转换类型
- COCO ↔ YOLO
- COCO ↔ Pascal VOC
- YOLO ↔ Pascal VOC
- LabelMe ↔ COCO
- LabelMe ↔ YOLO
- ImageNet ↔ YOLO分类

## 安装和启动

### 1. 安装依赖

确保已安装Python 3.9或更高版本，然后安装依赖包：

```bash
# 使用pip安装
pip install -r requirements.txt

# 或使用conda
conda env create -f environment.yml
conda activate dataset-converter
```

### 2. 启动GUI

#### Windows系统
双击运行 `start_gui.bat` 或在命令行中执行：
```cmd
python gui_main.py
```

#### Linux/macOS系统
```bash
# 给脚本执行权限
chmod +x start_gui.sh
# 运行脚本
./start_gui.sh

# 或直接运行
python3 gui_main.py
```

## 界面介绍

### 主界面布局

GUI界面分为两个主要区域：

1. **左侧：转换配置区域**
   - 转换类型选择
   - 参数配置
   - 进度显示和日志

2. **右侧：数据集预览区域**
   - 基本信息
   - 类别信息
   - 样本预览

### 菜单栏功能

#### 文件菜单
- **新建配置** (Ctrl+N) - 创建新的转换配置
- **打开配置** (Ctrl+O) - 加载保存的配置文件
- **保存配置** (Ctrl+S) - 保存当前配置
- **退出** (Ctrl+Q) - 退出应用程序

#### 工具菜单
- **批量转换** - 批量处理多个数据集（开发中）
- **数据集验证** - 验证数据集格式和完整性（开发中）
- **设置** - 应用程序设置（开发中）

#### 帮助菜单
- **使用说明** (F1) - 查看详细使用说明
- **关于** - 关于数据集转换器

## 使用步骤

### 1. 选择转换类型

在左上角的下拉菜单中选择需要的转换类型，例如"COCO → YOLO"。

### 2. 配置参数

根据选择的转换类型，界面会显示相应的参数配置选项：

#### 常见参数说明
- **输入文件/目录** - 源数据集的路径
- **输出目录** - 转换结果的保存路径
- **任务类型** - 检测、分割、关键点等
- **复制图像** - 是否复制图像文件到输出目录
- **数据集名称** - 转换后数据集的名称

#### 参数类型
- **文件选择** - 点击"浏览..."按钮选择文件
- **目录选择** - 点击"浏览..."按钮选择目录
- **下拉选择** - 从预定义选项中选择
- **复选框** - 启用/禁用某个功能
- **文本输入** - 手动输入文本或数值

### 3. 预览数据集（可选）

在右侧预览区域可以查看数据集的详细信息：

- **基本信息选项卡** - 显示数据集类型、图片数量、标注数量等
- **类别信息选项卡** - 显示所有类别的列表
- **样本预览选项卡** - 显示部分样本文件信息

### 4. 开始转换

1. 确认所有必填参数都已正确填写
2. 点击"开始转换"按钮
3. 在进度区域查看转换状态和日志
4. 转换完成后会显示成功或失败的消息

### 5. 保存配置（可选）

如果需要重复使用相同的转换配置，可以：
1. 点击菜单栏"文件" → "保存配置"
2. 选择保存位置和文件名
3. 下次可以通过"打开配置"快速加载

## 转换示例

### COCO转YOLO检测

1. 选择转换类型："COCO → YOLO"
2. 配置参数：
   - COCO标注文件：选择 `annotations.json`
   - 输出目录：选择输出文件夹
   - 任务类型：选择 "detection"
   - 复制图像：根据需要勾选
   - 图像目录：如果COCO文件中没有指定图像路径，需要手动选择
3. 点击"开始转换"

### YOLO转COCO分割

1. 选择转换类型："YOLO → COCO"
2. 配置参数：
   - YOLO数据集目录：选择包含images和labels文件夹的目录
   - 输出COCO文件：选择保存位置和文件名
   - 任务类型：选择 "segmentation"
   - 数据集名称：输入数据集名称
3. 点击"开始转换"

## 注意事项

### 文件路径
- 确保所有文件路径都是正确的，且文件存在
- 避免使用包含特殊字符的路径
- 建议使用英文路径名

### 数据集格式
- 确保输入数据集格式正确且完整
- COCO格式需要JSON标注文件和对应的图像
- YOLO格式需要TXT标注文件、图像文件和classes.txt
- VOC格式需要XML标注文件和图像文件

### 输出目录
- 确保输出目录有足够的磁盘空间
- 输出目录应该是空的或者确认可以覆盖
- 建议为每次转换创建新的输出目录

### 性能考虑
- 大型数据集转换可能需要较长时间
- 转换过程中避免关闭程序或计算机
- 可以通过日志查看转换进度

## 故障排除

### 常见问题

#### 1. 启动失败
- 检查Python版本是否为3.9或更高
- 确认所有依赖包都已正确安装
- 运行 `pip install -r requirements.txt` 重新安装依赖

#### 2. 转换失败
- 检查输入文件路径是否正确
- 确认数据集格式是否符合要求
- 查看错误日志了解具体问题

#### 3. 界面显示异常
- 尝试重启应用程序
- 检查系统是否支持PySide6
- 更新显卡驱动程序

#### 4. 文件选择问题
- 确保有足够的文件访问权限
- 避免选择系统保护的目录
- 检查文件是否被其他程序占用

### 获取帮助

如果遇到问题，可以：
1. 查看应用程序内的帮助文档
2. 检查转换日志中的错误信息
3. 参考命令行版本的使用说明
4. 联系开发团队获取技术支持

## 更新日志

### v1.0.0
- 初始版本发布
- 支持基本的数据集格式转换
- 提供图形化用户界面
- 支持配置保存和加载
- 提供数据集预览功能

## 许可证

本软件遵循开源许可证，详情请参考项目根目录下的LICENSE文件。
