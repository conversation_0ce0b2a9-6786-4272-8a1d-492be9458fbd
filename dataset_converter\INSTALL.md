# 安装指南

## 快速安装（推荐）

### 1. 自动安装脚本

运行自动设置脚本，它会检查环境并安装所有依赖：

```bash
python conda_setup.py
```

### 2. 使用conda环境文件

```bash
# 创建新环境
conda env create -f environment.yml

# 激活环境
conda activate dataset-converter

# 验证安装
python test_imports.py
```

## 手动安装

### 1. 创建conda环境

```bash
# 创建新的conda环境
conda create -n dataset-converter python=3.8 -y

# 激活环境
conda activate dataset-converter
```

### 2. 安装核心依赖

```bash
# 使用conda安装主要依赖
conda install -c conda-forge numpy pillow opencv matplotlib scikit-image lxml tqdm pyyaml -y
```

### 3. 安装特殊依赖

```bash
# 使用pip安装pycocotools（conda版本可能有问题）
pip install pycocotools
```

### 4. 验证安装

```bash
python test_imports.py
```

## 使用脚本安装

### Windows

```cmd
install_dependencies.bat
```

### Linux/macOS

```bash
bash install_dependencies.sh
```

## 故障排除

### 常见问题

1. **pycocotools安装失败**
   ```bash
   # Windows用户可能需要安装Visual Studio Build Tools
   # 或使用预编译版本
   conda install -c conda-forge pycocotools
   ```

2. **OpenCV导入错误**
   ```bash
   # 卸载可能冲突的opencv版本
   pip uninstall opencv-python opencv-contrib-python
   conda install -c conda-forge opencv
   ```

3. **权限错误**
   ```bash
   # 使用用户安装
   pip install --user pycocotools
   ```

### 依赖版本要求

| 包名 | 最低版本 | 推荐安装方式 |
|------|----------|--------------|
| Python | 3.7+ | conda |
| NumPy | 1.19.0+ | conda |
| Pillow | 8.0.0+ | conda |
| OpenCV | 4.5.0+ | conda |
| PyYAML | 5.4.0+ | conda |
| pycocotools | 2.0.2+ | pip |
| lxml | 4.6.0+ | conda |
| matplotlib | 3.3.0+ | conda |
| scikit-image | 0.18.0+ | conda |
| tqdm | 4.60.0+ | conda |

## 验证安装

运行以下命令确保所有组件正常工作：

```bash
# 测试模块导入
python test_imports.py

# 运行主程序
python convert_dataset.py

# 查看帮助
python convert_dataset.py --help-all
```

## 环境管理

### 激活环境
```bash
conda activate dataset-converter
```

### 停用环境
```bash
conda deactivate
```

### 删除环境
```bash
conda env remove -n dataset-converter
```

### 导出环境
```bash
conda env export > my_environment.yml
```

## 更新依赖

```bash
# 更新所有conda包
conda update --all

# 更新特定包
conda update numpy pillow opencv

# 更新pip包
pip install --upgrade pycocotools
```

## 离线安装

如果您在没有网络连接的环境中：

1. 在有网络的机器上下载包：
```bash
conda env export > environment.yml
```

2. 使用conda-pack打包环境：
```bash
conda install conda-pack
conda pack -n dataset-converter
```

3. 在目标机器上解压并激活环境。

## 获取帮助

如果遇到安装问题：

1. 检查conda和pip版本
2. 查看错误日志
3. 尝试在新的虚拟环境中安装
4. 参考官方文档或提交issue
