#!/usr/bin/env python3
"""
通用数据集转换器
不同计算机视觉数据集格式转换的主入口点
"""
import os
import argparse
import sys
from pathlib import Path

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from converters.coco_to_yolo import main as coco_to_yolo_main
from converters.yolo_to_coco import main as yolo_to_coco_main
from converters.voc_to_coco import main as voc_to_coco_main
from converters.coco_to_voc import main as coco_to_voc_main
from converters.yolo_to_voc import main as yolo_to_voc_main
from converters.voc_to_yolo import main as voc_to_yolo_main
from converters.labelme_to_coco import main as labelme_to_coco_main
from converters.labelme_to_yolo import main as labelme_to_yolo_main
from converters.coco_to_labelme import main as coco_to_labelme_main
from converters.yolo_to_labelme import main as yolo_to_labelme_main
from converters.imagenet_to_yolo_cls import main as imagenet_to_yolo_cls_main
from converters.yolo_cls_to_imagenet import main as yolo_cls_to_imagenet_main


def print_banner():
    """打印应用程序横幅"""
    print("=" * 60)
    print("           通用数据集转换器")
    print("    轻松转换CV数据集格式")
    print("=" * 60)
    print()


def print_supported_conversions():
    """打印支持的转换类型"""
    print("支持的转换:")
    print("=" * 30)
    print("目标检测和分割:")
    print("  • COCO ↔ YOLO")
    print("  • COCO ↔ Pascal VOC")
    print("  • YOLO ↔ Pascal VOC")
    print("  • LabelMe ↔ COCO")
    print("  • LabelMe ↔ YOLO")
    print()
    print("分类:")
    print("  • ImageNet ↔ YOLO分类")
    print()
    print("特殊功能:")
    print("  • OBB（有向边界框）支持")
    print("  • 关键点/姿态检测支持")
    print("  • 实例分割支持")
    print("  • LabelMe格式支持")
    print("=" * 30)
    print()


def get_conversion_choice():
    """获取用户的转换选择"""
    print("选择转换类型:")
    print("1. COCO → YOLO")
    print("2. YOLO → COCO")
    print("3. Pascal VOC → COCO")
    print("4. COCO → Pascal VOC")
    print("5. YOLO → Pascal VOC")
    print("6. Pascal VOC → YOLO")
    print("7. LabelMe → COCO")
    print("8. LabelMe → YOLO")
    print("9. COCO → LabelMe")
    print("10. YOLO → LabelMe")
    print("11. ImageNet → YOLO分类")
    print("12. YOLO分类 → ImageNet")
    print("13. 显示详细帮助")
    print("0. 退出")
    print()

    while True:
        try:
            choice = int(input("请输入您的选择 (0-13): "))
            if 0 <= choice <= 13:
                return choice
            else:
                print("请输入0到13之间的数字。")
        except ValueError:
            print("请输入有效的数字。")


def show_detailed_help():
    """Show detailed help information"""
    print("\nDetailed Help:")
    print("=" * 50)
    print()
    print("1. COCO → YOLO:")
    print("   Convert COCO JSON annotations to YOLO TXT format")
    print("   Supports: detection, segmentation, pose")
    print("   Example: python converters/coco_to_yolo.py --coco_json annotations.json --output_dir yolo_dataset")
    print()
    
    print("2. YOLO → COCO:")
    print("   Convert YOLO TXT annotations to COCO JSON format")
    print("   Supports: detection, segmentation, pose")
    print("   Example: python converters/yolo_to_coco.py --yolo_dir yolo_dataset --images_dir images --output_file coco.json")
    print()
    
    print("3. Pascal VOC → COCO:")
    print("   Convert Pascal VOC XML annotations to COCO JSON format")
    print("   Supports: detection, segmentation")
    print("   Example: python converters/voc_to_coco.py --voc_dir voc_dataset --output_file coco.json")
    print()
    
    print("4. COCO → Pascal VOC:")
    print("   Convert COCO JSON annotations to Pascal VOC XML format")
    print("   Supports: detection, segmentation")
    print("   Example: python converters/coco_to_voc.py --coco_json annotations.json --images_dir images --output_dir voc_dataset")
    print()
    
    print("5. YOLO → Pascal VOC:")
    print("   Convert YOLO TXT annotations to Pascal VOC XML format")
    print("   Supports: detection, segmentation, obb")
    print("   Example: python converters/yolo_to_voc.py --yolo_dir yolo_dataset --images_dir images --output_dir voc_dataset")
    print()
    
    print("6. Pascal VOC → YOLO:")
    print("   Convert Pascal VOC XML annotations to YOLO TXT format")
    print("   Supports: detection, segmentation")
    print("   Example: python converters/voc_to_yolo.py --voc_dir voc_dataset --output_dir yolo_dataset")
    print()
    
    print("7. ImageNet → YOLO Classification:")
    print("   Convert ImageNet directory structure to YOLO classification format")
    print("   Example: python converters/imagenet_to_yolo_cls.py --imagenet_dir imagenet --output_dir yolo_cls")
    print()
    
    print("8. YOLO Classification → ImageNet:")
    print("   Convert YOLO classification format to ImageNet directory structure")
    print("   Example: python converters/yolo_cls_to_imagenet.py --yolo_dir yolo_cls --output_dir imagenet")
    print()
    
    print("Common Parameters:")
    print("  --copy_images: Copy images to output directory")
    print("  --task: Specify task type (detection, segmentation, pose, obb)")
    print("  --help: Show detailed help for each converter")
    print()


def run_converter(choice):
    """Run the selected converter"""
    converters = {
        1: ("COCO → YOLO", "converters/coco_to_yolo.py"),
        2: ("YOLO → COCO", "converters/yolo_to_coco.py"),
        3: ("Pascal VOC → COCO", "converters/voc_to_coco.py"),
        4: ("COCO → Pascal VOC", "converters/coco_to_voc.py"),
        5: ("YOLO → Pascal VOC", "converters/yolo_to_voc.py"),
        6: ("Pascal VOC → YOLO", "converters/voc_to_yolo.py"),
        7: ("ImageNet → YOLO Classification", "converters/imagenet_to_yolo_cls.py"),
        8: ("YOLO Classification → ImageNet", "converters/yolo_cls_to_imagenet.py"),
    }
    
    if choice in converters:
        name, script = converters[choice]
        print(f"\nSelected: {name}")
        print(f"Script: {script}")
        print()
        print("To run this converter, use:")
        print(f"python {script} --help")
        print()
        print("This will show you all available options for this specific converter.")
        return True
    
    return False


def interactive_mode():
    """Run in interactive mode"""
    print_banner()
    print_supported_conversions()
    
    while True:
        choice = get_conversion_choice()
        
        if choice == 0:
            print("Goodbye!")
            break
        elif choice == 9:
            show_detailed_help()
            input("\nPress Enter to continue...")
            print("\n" + "=" * 60 + "\n")
        else:
            if run_converter(choice):
                input("\nPress Enter to continue...")
                print("\n" + "=" * 60 + "\n")


def main():
    """Main function"""
    parser = argparse.ArgumentParser(
        description='Universal Dataset Converter for Computer Vision',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python convert_dataset.py                    # Interactive mode
  python convert_dataset.py --list            # List all converters
  python convert_dataset.py --help-all        # Show help for all converters

Individual Converters:
  python converters/coco_to_yolo.py --help
  python converters/yolo_to_coco.py --help
  python converters/voc_to_coco.py --help
  python converters/coco_to_voc.py --help
  python converters/yolo_to_voc.py --help
  python converters/voc_to_yolo.py --help
  python converters/imagenet_to_yolo_cls.py --help
  python converters/yolo_cls_to_imagenet.py --help
        """
    )
    
    parser.add_argument('--list', action='store_true',
                       help='List all available converters')
    parser.add_argument('--help-all', action='store_true',
                       help='Show detailed help for all converters')
    
    args = parser.parse_args()
    
    if args.list:
        print_banner()
        print_supported_conversions()
        print("Available converter scripts:")
        converters = [
            "converters/coco_to_yolo.py",
            "converters/yolo_to_coco.py", 
            "converters/voc_to_coco.py",
            "converters/coco_to_voc.py",
            "converters/yolo_to_voc.py",
            "converters/voc_to_yolo.py",
            "converters/imagenet_to_yolo_cls.py",
            "converters/yolo_cls_to_imagenet.py"
        ]
        for converter in converters:
            print(f"  • {converter}")
        print("\nUse --help with any converter to see its options.")
        
    elif args.help_all:
        show_detailed_help()
        
    else:
        # Run in interactive mode
        interactive_mode()


if __name__ == '__main__':
    main()
