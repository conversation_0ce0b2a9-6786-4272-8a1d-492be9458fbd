#!/usr/bin/env python3
"""
将COCO格式数据集转换为LabelMe格式
支持：目标检测、实例分割
"""
import os
import argparse
import shutil
import base64
from pathlib import Path
from typing import Dict, List, Optional
from tqdm import tqdm

import sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.coco_utils import COCODataset
from utils.labelme_utils import (
    LabelMeDataset, create_labelme_shape, bbox_to_labelme_shape,
    polygon_to_labelme_shape, convert_coco_polygon_to_labelme
)
from utils.common import ensure_dir


def encode_image_to_base64(image_path: str) -> Optional[str]:
    """将图像编码为base64字符串
    
    Args:
        image_path: 图像文件路径
    
    Returns:
        base64编码的图像数据，失败返回None
    """
    try:
        with open(image_path, 'rb') as f:
            image_data = base64.b64encode(f.read()).decode('utf-8')
        return image_data
    except Exception as e:
        print(f"编码图像失败 {image_path}: {e}")
        return None


def convert_coco_to_labelme(coco_json: str, images_dir: str, output_dir: str,
                           include_image_data: bool = False,
                           task: str = 'detection') -> None:
    """将COCO数据集转换为LabelMe格式
    
    Args:
        coco_json: COCO标注JSON文件路径
        images_dir: 图像目录路径
        output_dir: 输出LabelMe数据集目录
        include_image_data: 是否在JSON中包含图像数据
        task: 任务类型 ('detection', 'segmentation', 'mixed')
    """
    # 加载COCO数据集
    print(f"加载COCO数据集: {coco_json}")
    coco_dataset = COCODataset(coco_json)
    
    # 创建输出目录
    output_dir = Path(output_dir)
    ensure_dir(output_dir)
    
    # 获取类别映射
    id_to_category = {cat['id']: cat['name'] for cat in coco_dataset.categories}
    
    print(f"转换 {len(coco_dataset.images)} 张图像...")
    
    converted_count = 0
    
    for image_info in tqdm(coco_dataset.images):
        image_id = image_info['id']
        filename = image_info['file_name']
        img_width = image_info['width']
        img_height = image_info['height']
        
        # 获取该图像的所有标注
        annotations = coco_dataset.get_annotations_by_image_id(image_id)
        
        if not annotations:
            continue  # 跳过没有标注的图像
        
        # 构建图像路径
        image_path = Path(images_dir) / filename
        
        # 准备LabelMe形状列表
        shapes = []
        
        for ann in annotations:
            category_id = ann['category_id']
            class_name = id_to_category.get(category_id, f"class_{category_id}")
            
            if task in ['detection', 'mixed'] and 'bbox' in ann:
                # 处理检测标注
                if 'segmentation' not in ann or task == 'detection':
                    # 纯检测或强制检测模式
                    coco_bbox = ann['bbox']  # [x, y, width, height]
                    shape = bbox_to_labelme_shape(class_name, coco_bbox, 'rectangle')
                    shapes.append(shape)
                    
            if task in ['segmentation', 'mixed'] and 'segmentation' in ann:
                # 处理分割标注
                segmentation = ann['segmentation']
                if isinstance(segmentation, list) and segmentation:
                    # 多边形格式
                    for polygon in segmentation:
                        if len(polygon) >= 6:  # 至少3个点
                            labelme_points = convert_coco_polygon_to_labelme(polygon)
                            shape = create_labelme_shape(class_name, 'polygon', labelme_points)
                            shapes.append(shape)
                # 注意：RLE格式的分割掩码需要额外处理，这里暂时跳过
        
        if shapes:
            # 准备图像数据
            image_data = None
            if include_image_data and image_path.exists():
                image_data = encode_image_to_base64(str(image_path))
            
            # 创建LabelMe标注
            labelme_dataset = LabelMeDataset()
            annotation = labelme_dataset.create_annotation(
                str(image_path), shapes, image_data
            )
            
            # 保存LabelMe标注文件
            json_filename = f"{Path(filename).stem}.json"
            output_json_path = output_dir / json_filename
            labelme_dataset.save_annotation(annotation, str(output_json_path))
            
            # 复制图像文件（如果不包含图像数据）
            if not include_image_data and image_path.exists():
                output_image_path = output_dir / filename
                shutil.copy2(image_path, output_image_path)
            
            converted_count += 1
    
    print(f"转换完成！输出保存到: {output_dir}")
    print(f"成功转换 {converted_count} 张图像")
    print(f"类别: {sorted(id_to_category.values())}")


def convert_coco_split_to_labelme(coco_train_json: str, coco_val_json: str,
                                 train_images_dir: str, val_images_dir: str,
                                 output_dir: str, include_image_data: bool = False,
                                 task: str = 'detection') -> None:
    """将COCO训练/验证集转换为LabelMe格式
    
    Args:
        coco_train_json: COCO训练集标注文件
        coco_val_json: COCO验证集标注文件
        train_images_dir: 训练图像目录
        val_images_dir: 验证图像目录
        output_dir: 输出目录
        include_image_data: 是否包含图像数据
        task: 任务类型
    """
    output_dir = Path(output_dir)
    
    # 创建分割目录
    train_output_dir = output_dir / 'train'
    val_output_dir = output_dir / 'val'
    
    datasets = [
        ('train', coco_train_json, train_images_dir, train_output_dir),
        ('val', coco_val_json, val_images_dir, val_output_dir)
    ]
    
    total_converted = 0
    
    for split_name, coco_json, images_dir, split_output_dir in datasets:
        if not os.path.exists(coco_json):
            print(f"跳过 {split_name} 集 - 文件不存在: {coco_json}")
            continue
            
        print(f"\n处理 {split_name} 集...")
        
        # 转换该分割
        convert_coco_to_labelme(
            coco_json, images_dir, str(split_output_dir),
            include_image_data, task
        )
        
        # 统计转换的文件数
        json_files = list(split_output_dir.glob('*.json'))
        split_count = len(json_files)
        total_converted += split_count
        
        print(f"{split_name} 集转换了 {split_count} 张图像")
    
    print(f"\n总计转换 {total_converted} 张图像")


def main():
    parser = argparse.ArgumentParser(description='将COCO数据集转换为LabelMe格式')
    parser.add_argument('--coco_json', help='COCO标注JSON文件路径')
    parser.add_argument('--coco_train_json', help='COCO训练集标注JSON文件路径')
    parser.add_argument('--coco_val_json', help='COCO验证集标注JSON文件路径')
    parser.add_argument('--images_dir', help='图像目录路径')
    parser.add_argument('--train_images_dir', help='训练图像目录路径')
    parser.add_argument('--val_images_dir', help='验证图像目录路径')
    parser.add_argument('--output_dir', required=True, help='输出LabelMe数据集目录路径')
    parser.add_argument('--task', choices=['detection', 'segmentation', 'mixed'], 
                       default='mixed', help='任务类型')
    parser.add_argument('--include_image_data', action='store_true',
                       help='在JSON文件中包含base64编码的图像数据')
    
    args = parser.parse_args()
    
    # 检查输入参数
    if args.coco_json and args.images_dir:
        # 单个数据集转换
        convert_coco_to_labelme(
            args.coco_json, args.images_dir, args.output_dir,
            args.include_image_data, args.task
        )
    elif args.coco_train_json and args.coco_val_json:
        # 分割数据集转换
        convert_coco_split_to_labelme(
            args.coco_train_json, args.coco_val_json,
            args.train_images_dir, args.val_images_dir,
            args.output_dir, args.include_image_data, args.task
        )
    else:
        parser.error("请提供 --coco_json 和 --images_dir，或者提供 "
                    "--coco_train_json, --coco_val_json, --train_images_dir, 和 --val_images_dir")


if __name__ == '__main__':
    main()
