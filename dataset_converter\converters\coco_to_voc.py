#!/usr/bin/env python3
"""
Convert COCO format dataset to Pascal VOC format
Supports: Object Detection, Instance Segmentation
"""
import os
import argparse
import shutil
from pathlib import Path
from typing import Dict, List, Optional
from tqdm import tqdm

import sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.coco_utils import COCODataset
from utils.voc_utils import VOCDataset, convert_coco_bbox_to_voc, create_voc_directory_structure, split_dataset
from utils.common import ensure_dir, copy_images


def convert_coco_to_voc(coco_json: str, images_dir: str, output_dir: str, 
                       task: str = 'detection', copy_images_flag: bool = True) -> None:
    """Convert COCO dataset to Pascal VOC format"""
    
    # Load COCO dataset
    print(f"Loading COCO dataset from: {coco_json}")
    coco_dataset = COCODataset(coco_json)
    
    # Create VOC directory structure
    voc_dirs = create_voc_directory_structure(output_dir)
    
    # Initialize VOC dataset
    voc_dataset = VOCDataset()
    
    # Get category mapping
    id_to_category = {cat['id']: cat['name'] for cat in coco_dataset.categories}
    
    print(f"Converting {len(coco_dataset.images)} images...")
    
    converted_images = []
    
    for image_info in tqdm(coco_dataset.images):
        image_id = image_info['id']
        filename = image_info['file_name']
        img_width = image_info['width']
        img_height = image_info['height']
        
        # Get annotations for this image
        annotations = coco_dataset.get_annotations_by_image_id(image_id)
        
        if not annotations:
            continue  # Skip images without annotations
        
        # Convert annotations to VOC format
        voc_annotations = []
        
        for ann in annotations:
            category_id = ann['category_id']
            class_name = id_to_category.get(category_id, f"class_{category_id}")
            
            if task == 'detection' and 'bbox' in ann:
                # Convert COCO bbox to VOC bbox
                coco_bbox = ann['bbox']  # [x, y, width, height]
                voc_bbox = convert_coco_bbox_to_voc(coco_bbox)  # [xmin, ymin, xmax, ymax]
                
                voc_annotation = {
                    'class_name': class_name,
                    'bbox': voc_bbox,
                    'truncated': 0,
                    'difficult': ann.get('iscrowd', 0),
                    'type': 'detection'
                }
                voc_annotations.append(voc_annotation)
                
            elif task == 'segmentation' and 'segmentation' in ann:
                # Handle segmentation
                coco_bbox = ann['bbox']
                voc_bbox = convert_coco_bbox_to_voc(coco_bbox)
                
                voc_annotation = {
                    'class_name': class_name,
                    'bbox': voc_bbox,
                    'truncated': 0,
                    'difficult': ann.get('iscrowd', 0),
                    'type': 'segmentation'
                }
                
                # Add polygon if available
                segmentation = ann['segmentation']
                if isinstance(segmentation, list) and segmentation:
                    # Use the first polygon
                    polygon = segmentation[0]
                    if len(polygon) >= 6:  # At least 3 points
                        voc_annotation['polygon'] = polygon
                
                voc_annotations.append(voc_annotation)
        
        if voc_annotations:
            # Create image info for VOC
            voc_image_info = {
                'filename': filename,
                'width': img_width,
                'height': img_height,
                'depth': 3
            }
            
            # Create XML annotation file
            xml_filename = f"{Path(filename).stem}.xml"
            xml_path = os.path.join(voc_dirs['annotations'], xml_filename)
            voc_dataset.create_annotation_xml(voc_image_info, voc_annotations, xml_path)
            
            converted_images.append(filename)
            
            # Copy image if requested
            if copy_images_flag and images_dir:
                src_image_path = Path(images_dir) / filename
                dst_image_path = Path(voc_dirs['images']) / filename
                if src_image_path.exists():
                    shutil.copy2(src_image_path, dst_image_path)
    
    # Create ImageSet files
    if converted_images:
        # Split dataset into train/val/test
        splits = split_dataset(converted_images, train_ratio=0.8, val_ratio=0.1, test_ratio=0.1)
        
        # Create ImageSet files
        for split_name, image_list in splits.items():
            if image_list:  # Only create file if there are images
                imageset_file = os.path.join(voc_dirs['imagesets_main'], f"{split_name}.txt")
                voc_dataset.create_imageset_file(image_list, imageset_file)
        
        # Create combined trainval set
        trainval_images = splits['train'] + splits['val']
        if trainval_images:
            trainval_file = os.path.join(voc_dirs['imagesets_main'], "trainval.txt")
            voc_dataset.create_imageset_file(trainval_images, trainval_file)
    
    print(f"Conversion completed! Output saved to: {output_dir}")
    print(f"Converted {len(converted_images)} images")
    print(f"Classes found: {sorted(set(id_to_category.values()))}")


def convert_coco_to_voc_with_split(coco_train_json: str, coco_val_json: str, 
                                  train_images_dir: str, val_images_dir: str,
                                  output_dir: str, task: str = 'detection',
                                  copy_images_flag: bool = True) -> None:
    """Convert COCO dataset with separate train/val splits to Pascal VOC format"""
    
    # Create VOC directory structure
    voc_dirs = create_voc_directory_structure(output_dir)
    
    # Initialize VOC dataset
    voc_dataset = VOCDataset()
    
    all_converted_images = {'train': [], 'val': []}
    all_classes = set()
    
    # Process train and val sets
    datasets = [
        ('train', coco_train_json, train_images_dir),
        ('val', coco_val_json, val_images_dir)
    ]
    
    for split_name, coco_json, images_dir in datasets:
        if not os.path.exists(coco_json):
            print(f"Skipping {split_name} set - file not found: {coco_json}")
            continue
            
        print(f"Processing {split_name} set...")
        
        # Load COCO dataset
        coco_dataset = COCODataset(coco_json)
        
        # Get category mapping
        id_to_category = {cat['id']: cat['name'] for cat in coco_dataset.categories}
        all_classes.update(id_to_category.values())
        
        converted_images = []
        
        for image_info in tqdm(coco_dataset.images):
            image_id = image_info['id']
            filename = image_info['file_name']
            img_width = image_info['width']
            img_height = image_info['height']
            
            # Get annotations for this image
            annotations = coco_dataset.get_annotations_by_image_id(image_id)
            
            if not annotations:
                continue
            
            # Convert annotations to VOC format
            voc_annotations = []
            
            for ann in annotations:
                category_id = ann['category_id']
                class_name = id_to_category.get(category_id, f"class_{category_id}")
                
                if task == 'detection' and 'bbox' in ann:
                    coco_bbox = ann['bbox']
                    voc_bbox = convert_coco_bbox_to_voc(coco_bbox)
                    
                    voc_annotation = {
                        'class_name': class_name,
                        'bbox': voc_bbox,
                        'truncated': 0,
                        'difficult': ann.get('iscrowd', 0),
                        'type': 'detection'
                    }
                    voc_annotations.append(voc_annotation)
                    
                elif task == 'segmentation' and 'segmentation' in ann:
                    coco_bbox = ann['bbox']
                    voc_bbox = convert_coco_bbox_to_voc(coco_bbox)
                    
                    voc_annotation = {
                        'class_name': class_name,
                        'bbox': voc_bbox,
                        'truncated': 0,
                        'difficult': ann.get('iscrowd', 0),
                        'type': 'segmentation'
                    }
                    
                    segmentation = ann['segmentation']
                    if isinstance(segmentation, list) and segmentation:
                        polygon = segmentation[0]
                        if len(polygon) >= 6:
                            voc_annotation['polygon'] = polygon
                    
                    voc_annotations.append(voc_annotation)
            
            if voc_annotations:
                # Create image info for VOC
                voc_image_info = {
                    'filename': filename,
                    'width': img_width,
                    'height': img_height,
                    'depth': 3
                }
                
                # Create XML annotation file
                xml_filename = f"{Path(filename).stem}.xml"
                xml_path = os.path.join(voc_dirs['annotations'], xml_filename)
                voc_dataset.create_annotation_xml(voc_image_info, voc_annotations, xml_path)
                
                converted_images.append(filename)
                
                # Copy image if requested
                if copy_images_flag and images_dir:
                    src_image_path = Path(images_dir) / filename
                    dst_image_path = Path(voc_dirs['images']) / filename
                    if src_image_path.exists():
                        shutil.copy2(src_image_path, dst_image_path)
        
        all_converted_images[split_name] = converted_images
        print(f"Converted {len(converted_images)} images for {split_name} set")
    
    # Create ImageSet files
    for split_name, image_list in all_converted_images.items():
        if image_list:
            imageset_file = os.path.join(voc_dirs['imagesets_main'], f"{split_name}.txt")
            voc_dataset.create_imageset_file(image_list, imageset_file)
    
    # Create combined trainval set
    trainval_images = all_converted_images['train'] + all_converted_images['val']
    if trainval_images:
        trainval_file = os.path.join(voc_dirs['imagesets_main'], "trainval.txt")
        voc_dataset.create_imageset_file(trainval_images, trainval_file)
    
    total_images = sum(len(images) for images in all_converted_images.values())
    print(f"Conversion completed! Output saved to: {output_dir}")
    print(f"Total converted images: {total_images}")
    print(f"Classes found: {sorted(all_classes)}")


def main():
    parser = argparse.ArgumentParser(description='Convert COCO dataset to Pascal VOC format')
    parser.add_argument('--coco_json', help='Path to COCO annotation JSON file')
    parser.add_argument('--coco_train_json', help='Path to COCO train annotation JSON file')
    parser.add_argument('--coco_val_json', help='Path to COCO val annotation JSON file')
    parser.add_argument('--images_dir', help='Path to images directory')
    parser.add_argument('--train_images_dir', help='Path to train images directory')
    parser.add_argument('--val_images_dir', help='Path to val images directory')
    parser.add_argument('--output_dir', required=True, help='Output directory for VOC dataset')
    parser.add_argument('--task', choices=['detection', 'segmentation'], 
                       default='detection', help='Task type')
    parser.add_argument('--copy_images', action='store_true', 
                       help='Copy images to output directory')
    
    args = parser.parse_args()
    
    # Check if using single dataset or split datasets
    if args.coco_json and args.images_dir:
        convert_coco_to_voc(args.coco_json, args.images_dir, args.output_dir, 
                           args.task, args.copy_images)
    elif args.coco_train_json and args.coco_val_json:
        convert_coco_to_voc_with_split(
            args.coco_train_json, args.coco_val_json,
            args.train_images_dir, args.val_images_dir,
            args.output_dir, args.task, args.copy_images
        )
    else:
        parser.error("Either provide --coco_json and --images_dir, or provide "
                    "--coco_train_json, --coco_val_json, --train_images_dir, and --val_images_dir")


if __name__ == '__main__':
    main()
