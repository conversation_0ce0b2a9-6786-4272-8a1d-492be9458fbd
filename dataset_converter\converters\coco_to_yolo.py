#!/usr/bin/env python3
"""
Convert COCO format dataset to YOLO format
Supports: Object Detection, Instance Segmentation, Keypoint Detection
"""
import os
import argparse
import shutil
from pathlib import Path
from typing import Dict, List, Optional
from tqdm import tqdm
import yaml

import sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.coco_utils import COCODataset
from utils.yolo_utils import (
    YOLODataset, write_yolo_detection_annotation, write_yolo_segmentation_annotation,
    write_yolo_pose_annotation, create_yolo_dataset_yaml
)
from utils.common import ensure_dir, get_image_size, polygon_to_bbox


def convert_coco_to_yolo_detection(coco_dataset: COCODataset, output_dir: str, 
                                  copy_images: bool = True) -> None:
    """Convert COCO detection dataset to YOLO format"""
    output_dir = Path(output_dir)
    
    # Create YOLO directory structure
    images_dir = output_dir / 'images'
    labels_dir = output_dir / 'labels'
    ensure_dir(images_dir)
    ensure_dir(labels_dir)
    
    # Get category mapping
    category_mapping = {cat['id']: idx for idx, cat in enumerate(coco_dataset.categories)}
    class_names = [cat['name'] for cat in coco_dataset.categories]
    
    # Save classes file
    with open(output_dir / 'classes.txt', 'w', encoding='utf-8') as f:
        for class_name in class_names:
            f.write(f"{class_name}\n")
    
    print(f"Converting {len(coco_dataset.images)} images...")
    
    for image_info in tqdm(coco_dataset.images):
        image_id = image_info['id']
        filename = image_info['file_name']
        img_width = image_info['width']
        img_height = image_info['height']
        
        # Get annotations for this image
        annotations = coco_dataset.get_annotations_by_image_id(image_id)
        
        # Convert annotations to YOLO format
        yolo_annotations = []
        for ann in annotations:
            if 'bbox' in ann:
                # Convert COCO bbox [x, y, width, height] to YOLO [x_center, y_center, width, height] normalized
                coco_bbox = ann['bbox']
                x_center = (coco_bbox[0] + coco_bbox[2] / 2) / img_width
                y_center = (coco_bbox[1] + coco_bbox[3] / 2) / img_height
                width = coco_bbox[2] / img_width
                height = coco_bbox[3] / img_height
                
                # Map category ID
                coco_cat_id = ann['category_id']
                yolo_class_id = category_mapping.get(coco_cat_id, 0)
                
                yolo_annotations.append({
                    'class_id': yolo_class_id,
                    'bbox': [x_center, y_center, width, height],
                    'type': 'detection'
                })
        
        # Save YOLO annotation file
        annotation_file = labels_dir / f"{Path(filename).stem}.txt"
        write_yolo_detection_annotation(yolo_annotations, str(annotation_file))
        
        # Copy image if requested
        if copy_images:
            # Assume images are in the same directory as annotation file or provided separately
            # This would need to be adjusted based on actual COCO dataset structure
            dst_image_path = images_dir / filename
            # Note: Source image path would need to be provided or inferred
            # shutil.copy2(src_image_path, dst_image_path)
    
    # Create dataset YAML
    yaml_config = create_yolo_dataset_yaml(
        classes=class_names,
        train_path='images',
        val_path='images'  # Same path, would typically be split
    )
    
    with open(output_dir / 'data.yaml', 'w', encoding='utf-8') as f:
        yaml.dump(yaml_config, f, default_flow_style=False)
    
    print(f"Conversion completed! Output saved to: {output_dir}")


def convert_coco_to_yolo_segmentation(coco_dataset: COCODataset, output_dir: str,
                                     copy_images: bool = True) -> None:
    """Convert COCO segmentation dataset to YOLO format"""
    output_dir = Path(output_dir)
    
    # Create YOLO directory structure
    images_dir = output_dir / 'images'
    labels_dir = output_dir / 'labels'
    ensure_dir(images_dir)
    ensure_dir(labels_dir)
    
    # Get category mapping
    category_mapping = {cat['id']: idx for idx, cat in enumerate(coco_dataset.categories)}
    class_names = [cat['name'] for cat in coco_dataset.categories]
    
    # Save classes file
    with open(output_dir / 'classes.txt', 'w', encoding='utf-8') as f:
        for class_name in class_names:
            f.write(f"{class_name}\n")
    
    print(f"Converting {len(coco_dataset.images)} images...")
    
    for image_info in tqdm(coco_dataset.images):
        image_id = image_info['id']
        filename = image_info['file_name']
        img_width = image_info['width']
        img_height = image_info['height']
        
        # Get annotations for this image
        annotations = coco_dataset.get_annotations_by_image_id(image_id)
        
        # Convert annotations to YOLO format
        yolo_annotations = []
        for ann in annotations:
            if 'segmentation' in ann and isinstance(ann['segmentation'], list):
                # Handle polygon segmentation
                for polygon in ann['segmentation']:
                    if len(polygon) >= 6:  # At least 3 points
                        # Normalize polygon coordinates
                        normalized_polygon = []
                        for i in range(0, len(polygon), 2):
                            if i + 1 < len(polygon):
                                x = polygon[i] / img_width
                                y = polygon[i + 1] / img_height
                                normalized_polygon.extend([x, y])
                        
                        # Map category ID
                        coco_cat_id = ann['category_id']
                        yolo_class_id = category_mapping.get(coco_cat_id, 0)
                        
                        yolo_annotations.append({
                            'class_id': yolo_class_id,
                            'polygon': normalized_polygon,
                            'type': 'segmentation'
                        })
        
        # Save YOLO annotation file
        annotation_file = labels_dir / f"{Path(filename).stem}.txt"
        write_yolo_segmentation_annotation(yolo_annotations, str(annotation_file))
        
        # Copy image if requested
        if copy_images:
            dst_image_path = images_dir / filename
            # Note: Source image path would need to be provided
    
    # Create dataset YAML
    yaml_config = create_yolo_dataset_yaml(
        classes=class_names,
        train_path='images',
        val_path='images'
    )
    
    with open(output_dir / 'data.yaml', 'w', encoding='utf-8') as f:
        yaml.dump(yaml_config, f, default_flow_style=False)
    
    print(f"Conversion completed! Output saved to: {output_dir}")


def convert_coco_to_yolo_pose(coco_dataset: COCODataset, output_dir: str,
                             copy_images: bool = True) -> None:
    """Convert COCO pose dataset to YOLO format"""
    output_dir = Path(output_dir)
    
    # Create YOLO directory structure
    images_dir = output_dir / 'images'
    labels_dir = output_dir / 'labels'
    ensure_dir(images_dir)
    ensure_dir(labels_dir)
    
    # Get category mapping (should be person for pose)
    category_mapping = {cat['id']: idx for idx, cat in enumerate(coco_dataset.categories)}
    class_names = [cat['name'] for cat in coco_dataset.categories]
    
    # Save classes file
    with open(output_dir / 'classes.txt', 'w', encoding='utf-8') as f:
        for class_name in class_names:
            f.write(f"{class_name}\n")
    
    print(f"Converting {len(coco_dataset.images)} images...")
    
    for image_info in tqdm(coco_dataset.images):
        image_id = image_info['id']
        filename = image_info['file_name']
        img_width = image_info['width']
        img_height = image_info['height']
        
        # Get annotations for this image
        annotations = coco_dataset.get_annotations_by_image_id(image_id)
        
        # Convert annotations to YOLO format
        yolo_annotations = []
        for ann in annotations:
            if 'keypoints' in ann and 'bbox' in ann:
                # Convert bbox to YOLO format
                coco_bbox = ann['bbox']
                x_center = (coco_bbox[0] + coco_bbox[2] / 2) / img_width
                y_center = (coco_bbox[1] + coco_bbox[3] / 2) / img_height
                width = coco_bbox[2] / img_width
                height = coco_bbox[3] / img_height
                
                # Convert keypoints to YOLO format
                keypoints = ann['keypoints']
                normalized_keypoints = []
                for i in range(0, len(keypoints), 3):
                    if i + 2 < len(keypoints):
                        x = keypoints[i] / img_width if keypoints[i] > 0 else 0
                        y = keypoints[i + 1] / img_height if keypoints[i + 1] > 0 else 0
                        v = keypoints[i + 2]  # visibility flag
                        normalized_keypoints.extend([x, y, v])
                
                # Map category ID
                coco_cat_id = ann['category_id']
                yolo_class_id = category_mapping.get(coco_cat_id, 0)
                
                yolo_annotations.append({
                    'class_id': yolo_class_id,
                    'bbox': [x_center, y_center, width, height],
                    'keypoints': normalized_keypoints,
                    'type': 'pose'
                })
        
        # Save YOLO annotation file
        annotation_file = labels_dir / f"{Path(filename).stem}.txt"
        write_yolo_pose_annotation(yolo_annotations, str(annotation_file))
        
        # Copy image if requested
        if copy_images:
            dst_image_path = images_dir / filename
    
    # Create dataset YAML
    yaml_config = create_yolo_dataset_yaml(
        classes=class_names,
        train_path='images',
        val_path='images'
    )
    
    with open(output_dir / 'data.yaml', 'w', encoding='utf-8') as f:
        yaml.dump(yaml_config, f, default_flow_style=False)
    
    print(f"Conversion completed! Output saved to: {output_dir}")


def main():
    parser = argparse.ArgumentParser(description='Convert COCO dataset to YOLO format')
    parser.add_argument('--coco_json', required=True, help='Path to COCO annotation JSON file')
    parser.add_argument('--images_dir', help='Path to COCO images directory')
    parser.add_argument('--output_dir', required=True, help='Output directory for YOLO dataset')
    parser.add_argument('--task', choices=['detection', 'segmentation', 'pose'], 
                       default='detection', help='Task type')
    parser.add_argument('--copy_images', action='store_true', 
                       help='Copy images to output directory')
    
    args = parser.parse_args()
    
    # Load COCO dataset
    print(f"Loading COCO dataset from: {args.coco_json}")
    coco_dataset = COCODataset(args.coco_json)
    
    # Convert based on task type
    if args.task == 'detection':
        convert_coco_to_yolo_detection(coco_dataset, args.output_dir, args.copy_images)
    elif args.task == 'segmentation':
        convert_coco_to_yolo_segmentation(coco_dataset, args.output_dir, args.copy_images)
    elif args.task == 'pose':
        convert_coco_to_yolo_pose(coco_dataset, args.output_dir, args.copy_images)


if __name__ == '__main__':
    main()
