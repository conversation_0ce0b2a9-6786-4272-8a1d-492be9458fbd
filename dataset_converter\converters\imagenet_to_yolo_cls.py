#!/usr/bin/env python3
"""
Convert ImageNet format dataset to YOLO classification format
Supports: Image Classification
"""
import os
import argparse
import shutil
from pathlib import Path
from typing import Dict, List, Optional
from tqdm import tqdm
import yaml

import sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.common import ensure_dir, get_image_files


def convert_imagenet_to_yolo_classification(imagenet_dir: str, output_dir: str,
                                           copy_images_flag: bool = True,
                                           train_ratio: float = 0.8,
                                           val_ratio: float = 0.1,
                                           test_ratio: float = 0.1) -> None:
    """Convert ImageNet format dataset to YOLO classification format"""
    imagenet_dir = Path(imagenet_dir)
    output_dir = Path(output_dir)
    
    # Create YOLO classification directory structure
    train_dir = output_dir / 'train'
    val_dir = output_dir / 'val'
    test_dir = output_dir / 'test'
    
    ensure_dir(train_dir)
    ensure_dir(val_dir)
    ensure_dir(test_dir)
    
    # Find all class directories
    class_dirs = [d for d in imagenet_dir.iterdir() if d.is_dir()]
    if not class_dirs:
        raise ValueError(f"No class directories found in: {imagenet_dir}")
    
    class_names = sorted([d.name for d in class_dirs])
    print(f"Found {len(class_names)} classes: {class_names}")
    
    # Process each class
    total_images = 0
    class_image_counts = {}
    
    for class_dir in tqdm(class_dirs, desc="Processing classes"):
        class_name = class_dir.name
        
        # Get all images in this class directory
        image_files = get_image_files(class_dir)
        if not image_files:
            print(f"Warning: No images found in class directory: {class_dir}")
            continue
        
        class_image_counts[class_name] = len(image_files)
        total_images += len(image_files)
        
        # Shuffle images for random split
        import random
        random.shuffle(image_files)
        
        # Calculate split indices
        num_images = len(image_files)
        train_end = int(num_images * train_ratio)
        val_end = train_end + int(num_images * val_ratio)
        
        # Split images
        train_images = image_files[:train_end]
        val_images = image_files[train_end:val_end]
        test_images = image_files[val_end:]
        
        # Create class directories in output splits
        train_class_dir = train_dir / class_name
        val_class_dir = val_dir / class_name
        test_class_dir = test_dir / class_name
        
        ensure_dir(train_class_dir)
        ensure_dir(val_class_dir)
        ensure_dir(test_class_dir)
        
        # Copy images to respective directories
        if copy_images_flag:
            # Copy train images
            for img_file in train_images:
                dst_path = train_class_dir / img_file.name
                shutil.copy2(img_file, dst_path)
            
            # Copy val images
            for img_file in val_images:
                dst_path = val_class_dir / img_file.name
                shutil.copy2(img_file, dst_path)
            
            # Copy test images
            for img_file in test_images:
                dst_path = test_class_dir / img_file.name
                shutil.copy2(img_file, dst_path)
        
        print(f"Class '{class_name}': {len(train_images)} train, {len(val_images)} val, {len(test_images)} test")
    
    # Create dataset YAML configuration
    yaml_config = {
        'path': str(output_dir),
        'train': 'train',
        'val': 'val',
        'test': 'test',
        'nc': len(class_names),
        'names': class_names
    }
    
    with open(output_dir / 'data.yaml', 'w', encoding='utf-8') as f:
        yaml.dump(yaml_config, f, default_flow_style=False)
    
    # Save class names
    with open(output_dir / 'classes.txt', 'w', encoding='utf-8') as f:
        for class_name in class_names:
            f.write(f"{class_name}\n")
    
    # Create summary
    summary = {
        'total_classes': len(class_names),
        'total_images': total_images,
        'class_distribution': class_image_counts,
        'split_ratios': {
            'train': train_ratio,
            'val': val_ratio,
            'test': test_ratio
        }
    }
    
    with open(output_dir / 'dataset_summary.yaml', 'w', encoding='utf-8') as f:
        yaml.dump(summary, f, default_flow_style=False)
    
    print(f"\nConversion completed! Output saved to: {output_dir}")
    print(f"Total images: {total_images}")
    print(f"Classes: {len(class_names)}")


def convert_imagenet_with_annotation_file(imagenet_dir: str, annotation_file: str, 
                                         output_dir: str, copy_images_flag: bool = True,
                                         train_ratio: float = 0.8, val_ratio: float = 0.1,
                                         test_ratio: float = 0.1) -> None:
    """Convert ImageNet format with annotation file to YOLO classification format"""
    imagenet_dir = Path(imagenet_dir)
    output_dir = Path(output_dir)
    
    # Read annotation file
    image_to_class = {}
    class_names = set()
    
    with open(annotation_file, 'r', encoding='utf-8') as f:
        for line in f:
            line = line.strip()
            if line:
                parts = line.split()
                if len(parts) >= 2:
                    image_name = parts[0]
                    class_name = ' '.join(parts[1:])  # Handle class names with spaces
                    image_to_class[image_name] = class_name
                    class_names.add(class_name)
    
    class_names = sorted(list(class_names))
    print(f"Found {len(class_names)} classes from annotation file")
    print(f"Found {len(image_to_class)} image annotations")
    
    # Create YOLO classification directory structure
    train_dir = output_dir / 'train'
    val_dir = output_dir / 'val'
    test_dir = output_dir / 'test'
    
    ensure_dir(train_dir)
    ensure_dir(val_dir)
    ensure_dir(test_dir)
    
    # Create class directories
    for class_name in class_names:
        ensure_dir(train_dir / class_name)
        ensure_dir(val_dir / class_name)
        ensure_dir(test_dir / class_name)
    
    # Get all images
    image_files = get_image_files(imagenet_dir)
    valid_images = []
    
    for img_file in image_files:
        if img_file.name in image_to_class:
            valid_images.append(img_file)
    
    print(f"Found {len(valid_images)} valid images with annotations")
    
    # Group images by class
    class_to_images = {}
    for img_file in valid_images:
        class_name = image_to_class[img_file.name]
        if class_name not in class_to_images:
            class_to_images[class_name] = []
        class_to_images[class_name].append(img_file)
    
    # Process each class
    total_images = 0
    class_image_counts = {}
    
    for class_name, images in tqdm(class_to_images.items(), desc="Processing classes"):
        # Shuffle images for random split
        import random
        random.shuffle(images)
        
        class_image_counts[class_name] = len(images)
        total_images += len(images)
        
        # Calculate split indices
        num_images = len(images)
        train_end = int(num_images * train_ratio)
        val_end = train_end + int(num_images * val_ratio)
        
        # Split images
        train_images = images[:train_end]
        val_images = images[train_end:val_end]
        test_images = images[val_end:]
        
        # Copy images to respective directories
        if copy_images_flag:
            # Copy train images
            for img_file in train_images:
                dst_path = train_dir / class_name / img_file.name
                shutil.copy2(img_file, dst_path)
            
            # Copy val images
            for img_file in val_images:
                dst_path = val_dir / class_name / img_file.name
                shutil.copy2(img_file, dst_path)
            
            # Copy test images
            for img_file in test_images:
                dst_path = test_dir / class_name / img_file.name
                shutil.copy2(img_file, dst_path)
        
        print(f"Class '{class_name}': {len(train_images)} train, {len(val_images)} val, {len(test_images)} test")
    
    # Create dataset YAML configuration
    yaml_config = {
        'path': str(output_dir),
        'train': 'train',
        'val': 'val',
        'test': 'test',
        'nc': len(class_names),
        'names': class_names
    }
    
    with open(output_dir / 'data.yaml', 'w', encoding='utf-8') as f:
        yaml.dump(yaml_config, f, default_flow_style=False)
    
    # Save class names
    with open(output_dir / 'classes.txt', 'w', encoding='utf-8') as f:
        for class_name in class_names:
            f.write(f"{class_name}\n")
    
    # Create summary
    summary = {
        'total_classes': len(class_names),
        'total_images': total_images,
        'class_distribution': class_image_counts,
        'split_ratios': {
            'train': train_ratio,
            'val': val_ratio,
            'test': test_ratio
        }
    }
    
    with open(output_dir / 'dataset_summary.yaml', 'w', encoding='utf-8') as f:
        yaml.dump(summary, f, default_flow_style=False)
    
    print(f"\nConversion completed! Output saved to: {output_dir}")
    print(f"Total images: {total_images}")
    print(f"Classes: {len(class_names)}")


def main():
    parser = argparse.ArgumentParser(description='Convert ImageNet format dataset to YOLO classification format')
    parser.add_argument('--imagenet_dir', required=True, help='Path to ImageNet dataset directory')
    parser.add_argument('--output_dir', required=True, help='Output directory for YOLO classification dataset')
    parser.add_argument('--annotation_file', help='Path to annotation file (image_name class_name format)')
    parser.add_argument('--copy_images', action='store_true', 
                       help='Copy images to output directory')
    parser.add_argument('--train_ratio', type=float, default=0.8, 
                       help='Ratio of images for training (default: 0.8)')
    parser.add_argument('--val_ratio', type=float, default=0.1, 
                       help='Ratio of images for validation (default: 0.1)')
    parser.add_argument('--test_ratio', type=float, default=0.1, 
                       help='Ratio of images for testing (default: 0.1)')
    
    args = parser.parse_args()
    
    # Validate ratios
    total_ratio = args.train_ratio + args.val_ratio + args.test_ratio
    if abs(total_ratio - 1.0) > 0.001:
        parser.error(f"Train, val, and test ratios must sum to 1.0, got {total_ratio}")
    
    # Convert based on whether annotation file is provided
    if args.annotation_file:
        convert_imagenet_with_annotation_file(
            args.imagenet_dir, args.annotation_file, args.output_dir,
            args.copy_images, args.train_ratio, args.val_ratio, args.test_ratio
        )
    else:
        convert_imagenet_to_yolo_classification(
            args.imagenet_dir, args.output_dir, args.copy_images,
            args.train_ratio, args.val_ratio, args.test_ratio
        )


if __name__ == '__main__':
    main()
