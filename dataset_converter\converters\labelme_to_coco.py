#!/usr/bin/env python3
"""
将LabelMe格式数据集转换为COCO格式
支持：目标检测、实例分割
"""
import os
import argparse
from pathlib import Path
from typing import Dict, List, Optional
from tqdm import tqdm

import sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.labelme_utils import LabelMeDataset, extract_image_from_labelme
from utils.coco_utils import COCODataset
from utils.common import ensure_dir, get_image_files, polygon_to_bbox


def convert_labelme_to_coco_detection(labelme_dir: str, output_file: str,
                                     extract_images: bool = False,
                                     images_output_dir: Optional[str] = None) -> None:
    """将LabelMe检测数据集转换为COCO格式
    
    Args:
        labelme_dir: LabelMe数据集目录
        output_file: 输出COCO JSON文件路径
        extract_images: 是否从JSON文件中提取图像
        images_output_dir: 图像输出目录（当extract_images=True时使用）
    """
    labelme_dir = Path(labelme_dir)
    
    # 查找所有JSON标注文件
    json_files = list(labelme_dir.glob('*.json'))
    if not json_files:
        raise ValueError(f"在目录中未找到JSON标注文件: {labelme_dir}")
    
    print(f"找到 {len(json_files)} 个标注文件")
    
    # 初始化LabelMe和COCO数据集
    labelme_dataset = LabelMeDataset(str(labelme_dir))
    coco_dataset = COCODataset()
    
    # 处理所有标注文件以收集类别信息
    all_classes = set()
    valid_annotations = {}
    
    print("处理标注文件...")
    for json_file in tqdm(json_files):
        image_info, annotations = labelme_dataset.read_annotation(str(json_file))
        if image_info and annotations:
            # 过滤检测标注
            detection_annotations = [ann for ann in annotations 
                                   if ann.get('type') in ['detection', 'segmentation']]
            if detection_annotations:
                valid_annotations[json_file.stem] = (image_info, detection_annotations)
                for ann in detection_annotations:
                    all_classes.add(ann['label'])
    
    if not valid_annotations:
        print("未找到有效的检测标注")
        return
    
    # 添加类别到COCO数据集
    class_to_id = {}
    for idx, class_name in enumerate(sorted(all_classes)):
        category_id = idx + 1  # COCO类别ID从1开始
        coco_dataset.add_category(category_id, class_name)
        class_to_id[class_name] = category_id
    
    print(f"找到 {len(all_classes)} 个类别: {sorted(all_classes)}")
    
    # 转换标注
    print("转换标注...")
    image_id = 1
    annotation_id = 1
    
    for image_name, (image_info, annotations) in tqdm(valid_annotations.items()):
        filename = image_info['filename']
        img_width = image_info['width']
        img_height = image_info['height']
        
        # 如果需要，提取图像
        if extract_images and images_output_dir:
            json_file = labelme_dir / f"{image_name}.json"
            extracted_path = extract_image_from_labelme(str(json_file), images_output_dir)
            if extracted_path:
                filename = os.path.basename(extracted_path)
        
        # 添加图像到COCO数据集
        coco_dataset.add_image(image_id, filename, img_width, img_height)
        
        # 转换标注
        for ann in annotations:
            label = ann['label']
            category_id = class_to_id[label]
            
            if ann.get('type') == 'detection' and 'bbox' in ann:
                # 目标检测标注
                bbox = ann['bbox']  # [x, y, width, height]
                coco_dataset.add_detection_annotation(
                    annotation_id, image_id, category_id, bbox
                )
                annotation_id += 1
                
            elif ann.get('type') == 'segmentation' and 'polygon' in ann:
                # 实例分割标注
                polygon = ann['polygon']
                bbox = polygon_to_bbox(polygon)
                
                coco_dataset.add_segmentation_annotation(
                    annotation_id, image_id, category_id, bbox, [polygon]
                )
                annotation_id += 1
        
        image_id += 1
    
    # 保存COCO数据集
    ensure_dir(os.path.dirname(output_file))
    coco_dataset.save_annotations(output_file)
    print(f"转换完成！输出保存到: {output_file}")
    print(f"转换了 {len(valid_annotations)} 张图像，{annotation_id - 1} 个标注")


def convert_labelme_to_coco_segmentation(labelme_dir: str, output_file: str,
                                        extract_images: bool = False,
                                        images_output_dir: Optional[str] = None) -> None:
    """将LabelMe分割数据集转换为COCO格式
    
    Args:
        labelme_dir: LabelMe数据集目录
        output_file: 输出COCO JSON文件路径
        extract_images: 是否从JSON文件中提取图像
        images_output_dir: 图像输出目录
    """
    labelme_dir = Path(labelme_dir)
    
    # 查找所有JSON标注文件
    json_files = list(labelme_dir.glob('*.json'))
    if not json_files:
        raise ValueError(f"在目录中未找到JSON标注文件: {labelme_dir}")
    
    print(f"找到 {len(json_files)} 个标注文件")
    
    # 初始化数据集
    labelme_dataset = LabelMeDataset(str(labelme_dir))
    coco_dataset = COCODataset()
    
    # 处理所有标注文件
    all_classes = set()
    valid_annotations = {}
    
    print("处理标注文件...")
    for json_file in tqdm(json_files):
        image_info, annotations = labelme_dataset.read_annotation(str(json_file))
        if image_info and annotations:
            # 过滤分割标注
            segmentation_annotations = [ann for ann in annotations 
                                      if ann.get('type') == 'segmentation' and 'polygon' in ann]
            if segmentation_annotations:
                valid_annotations[json_file.stem] = (image_info, segmentation_annotations)
                for ann in segmentation_annotations:
                    all_classes.add(ann['label'])
    
    if not valid_annotations:
        print("未找到有效的分割标注")
        return
    
    # 添加类别
    class_to_id = {}
    for idx, class_name in enumerate(sorted(all_classes)):
        category_id = idx + 1
        coco_dataset.add_category(category_id, class_name)
        class_to_id[class_name] = category_id
    
    print(f"找到 {len(all_classes)} 个类别: {sorted(all_classes)}")
    
    # 转换标注
    print("转换标注...")
    image_id = 1
    annotation_id = 1
    
    for image_name, (image_info, annotations) in tqdm(valid_annotations.items()):
        filename = image_info['filename']
        img_width = image_info['width']
        img_height = image_info['height']
        
        # 如果需要，提取图像
        if extract_images and images_output_dir:
            json_file = labelme_dir / f"{image_name}.json"
            extracted_path = extract_image_from_labelme(str(json_file), images_output_dir)
            if extracted_path:
                filename = os.path.basename(extracted_path)
        
        # 添加图像
        coco_dataset.add_image(image_id, filename, img_width, img_height)
        
        # 转换分割标注
        for ann in annotations:
            label = ann['label']
            category_id = class_to_id[label]
            polygon = ann['polygon']
            bbox = polygon_to_bbox(polygon)
            
            coco_dataset.add_segmentation_annotation(
                annotation_id, image_id, category_id, bbox, [polygon]
            )
            annotation_id += 1
        
        image_id += 1
    
    # 保存结果
    ensure_dir(os.path.dirname(output_file))
    coco_dataset.save_annotations(output_file)
    print(f"转换完成！输出保存到: {output_file}")
    print(f"转换了 {len(valid_annotations)} 张图像，{annotation_id - 1} 个标注")


def convert_labelme_mixed_to_coco(labelme_dir: str, output_file: str,
                                 extract_images: bool = False,
                                 images_output_dir: Optional[str] = None) -> None:
    """将包含混合标注类型的LabelMe数据集转换为COCO格式
    
    Args:
        labelme_dir: LabelMe数据集目录
        output_file: 输出COCO JSON文件路径
        extract_images: 是否从JSON文件中提取图像
        images_output_dir: 图像输出目录
    """
    labelme_dir = Path(labelme_dir)
    
    # 查找所有JSON标注文件
    json_files = list(labelme_dir.glob('*.json'))
    if not json_files:
        raise ValueError(f"在目录中未找到JSON标注文件: {labelme_dir}")
    
    print(f"找到 {len(json_files)} 个标注文件")
    
    # 初始化数据集
    labelme_dataset = LabelMeDataset(str(labelme_dir))
    coco_dataset = COCODataset()
    
    # 处理所有标注文件
    all_classes = set()
    valid_annotations = {}
    
    print("处理标注文件...")
    for json_file in tqdm(json_files):
        image_info, annotations = labelme_dataset.read_annotation(str(json_file))
        if image_info and annotations:
            # 包含检测和分割标注
            valid_anns = [ann for ann in annotations 
                         if ann.get('type') in ['detection', 'segmentation']]
            if valid_anns:
                valid_annotations[json_file.stem] = (image_info, valid_anns)
                for ann in valid_anns:
                    all_classes.add(ann['label'])
    
    if not valid_annotations:
        print("未找到有效的标注")
        return
    
    # 添加类别
    class_to_id = {}
    for idx, class_name in enumerate(sorted(all_classes)):
        category_id = idx + 1
        coco_dataset.add_category(category_id, class_name)
        class_to_id[class_name] = category_id
    
    print(f"找到 {len(all_classes)} 个类别: {sorted(all_classes)}")
    
    # 转换标注
    print("转换标注...")
    image_id = 1
    annotation_id = 1
    
    for image_name, (image_info, annotations) in tqdm(valid_annotations.items()):
        filename = image_info['filename']
        img_width = image_info['width']
        img_height = image_info['height']
        
        # 如果需要，提取图像
        if extract_images and images_output_dir:
            json_file = labelme_dir / f"{image_name}.json"
            extracted_path = extract_image_from_labelme(str(json_file), images_output_dir)
            if extracted_path:
                filename = os.path.basename(extracted_path)
        
        # 添加图像
        coco_dataset.add_image(image_id, filename, img_width, img_height)
        
        # 转换标注
        for ann in annotations:
            label = ann['label']
            category_id = class_to_id[label]
            
            if ann.get('type') == 'detection' and 'bbox' in ann:
                # 检测标注
                bbox = ann['bbox']
                coco_dataset.add_detection_annotation(
                    annotation_id, image_id, category_id, bbox
                )
                annotation_id += 1
                
            elif ann.get('type') == 'segmentation' and 'polygon' in ann:
                # 分割标注
                polygon = ann['polygon']
                bbox = polygon_to_bbox(polygon)
                coco_dataset.add_segmentation_annotation(
                    annotation_id, image_id, category_id, bbox, [polygon]
                )
                annotation_id += 1
        
        image_id += 1
    
    # 保存结果
    ensure_dir(os.path.dirname(output_file))
    coco_dataset.save_annotations(output_file)
    print(f"转换完成！输出保存到: {output_file}")
    print(f"转换了 {len(valid_annotations)} 张图像，{annotation_id - 1} 个标注")


def main():
    parser = argparse.ArgumentParser(description='将LabelMe数据集转换为COCO格式')
    parser.add_argument('--labelme_dir', required=True, help='LabelMe数据集目录路径')
    parser.add_argument('--output_file', required=True, help='输出COCO JSON文件路径')
    parser.add_argument('--task', choices=['detection', 'segmentation', 'mixed'], 
                       default='mixed', help='任务类型')
    parser.add_argument('--extract_images', action='store_true',
                       help='从JSON文件中提取图像数据')
    parser.add_argument('--images_output_dir', help='图像输出目录（当extract_images=True时使用）')
    
    args = parser.parse_args()
    
    # 如果需要提取图像但未指定输出目录，使用默认目录
    if args.extract_images and not args.images_output_dir:
        args.images_output_dir = os.path.join(os.path.dirname(args.output_file), 'images')
    
    # 根据任务类型进行转换
    if args.task == 'detection':
        convert_labelme_to_coco_detection(
            args.labelme_dir, args.output_file, 
            args.extract_images, args.images_output_dir
        )
    elif args.task == 'segmentation':
        convert_labelme_to_coco_segmentation(
            args.labelme_dir, args.output_file,
            args.extract_images, args.images_output_dir
        )
    elif args.task == 'mixed':
        convert_labelme_mixed_to_coco(
            args.labelme_dir, args.output_file,
            args.extract_images, args.images_output_dir
        )


if __name__ == '__main__':
    main()
