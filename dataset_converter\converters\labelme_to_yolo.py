#!/usr/bin/env python3
"""
将LabelMe格式数据集转换为YOLO格式
支持：目标检测、实例分割
"""
import os
import argparse
import shutil
from pathlib import Path
from typing import Dict, List, Optional
from tqdm import tqdm
import yaml

import sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.labelme_utils import LabelMeDataset, extract_image_from_labelme
from utils.yolo_utils import (
    write_yolo_detection_annotation, write_yolo_segmentation_annotation,
    create_yolo_dataset_yaml
)
from utils.common import ensure_dir, corner_to_center


def convert_labelme_to_yolo_detection(labelme_dir: str, output_dir: str,
                                     extract_images: bool = False) -> None:
    """将LabelMe检测数据集转换为YOLO格式
    
    Args:
        labelme_dir: LabelMe数据集目录
        output_dir: 输出YOLO数据集目录
        extract_images: 是否从JSON文件中提取图像
    """
    labelme_dir = Path(labelme_dir)
    output_dir = Path(output_dir)
    
    # 创建YOLO目录结构
    images_dir = output_dir / 'images'
    labels_dir = output_dir / 'labels'
    ensure_dir(images_dir)
    ensure_dir(labels_dir)
    
    # 查找所有JSON标注文件
    json_files = list(labelme_dir.glob('*.json'))
    if not json_files:
        raise ValueError(f"在目录中未找到JSON标注文件: {labelme_dir}")
    
    print(f"找到 {len(json_files)} 个标注文件")
    
    # 初始化LabelMe数据集
    labelme_dataset = LabelMeDataset(str(labelme_dir))
    
    # 处理所有标注文件以收集类别信息
    all_classes = set()
    valid_annotations = {}
    
    print("处理标注文件...")
    for json_file in tqdm(json_files):
        image_info, annotations = labelme_dataset.read_annotation(str(json_file))
        if image_info and annotations:
            # 过滤检测标注
            detection_annotations = [ann for ann in annotations 
                                   if ann.get('type') == 'detection' and 'bbox' in ann]
            if detection_annotations:
                valid_annotations[json_file.stem] = (image_info, detection_annotations)
                for ann in detection_annotations:
                    all_classes.add(ann['label'])
    
    if not valid_annotations:
        print("未找到有效的检测标注")
        return
    
    # 创建类别映射
    class_names = sorted(list(all_classes))
    class_to_id = {name: idx for idx, name in enumerate(class_names)}
    
    # 保存类别文件
    with open(output_dir / 'classes.txt', 'w', encoding='utf-8') as f:
        for class_name in class_names:
            f.write(f"{class_name}\n")
    
    print(f"找到 {len(class_names)} 个类别: {class_names}")
    
    # 转换标注
    print("转换标注...")
    converted_images = []
    
    for image_name, (image_info, annotations) in tqdm(valid_annotations.items()):
        filename = image_info['filename']
        img_width = image_info['width']
        img_height = image_info['height']
        
        if img_width == 0 or img_height == 0:
            print(f"跳过无效图像尺寸: {filename}")
            continue
        
        # 转换标注为YOLO格式
        yolo_annotations = []
        
        for ann in annotations:
            label = ann['label']
            class_id = class_to_id[label]
            bbox = ann['bbox']  # [x, y, width, height]
            
            # 转换为YOLO格式 [x_center, y_center, width, height] 归一化
            x_center = (bbox[0] + bbox[2] / 2) / img_width
            y_center = (bbox[1] + bbox[3] / 2) / img_height
            width = bbox[2] / img_width
            height = bbox[3] / img_height
            
            yolo_annotation = {
                'class_id': class_id,
                'bbox': [x_center, y_center, width, height],
                'type': 'detection'
            }
            yolo_annotations.append(yolo_annotation)
        
        if yolo_annotations:
            # 保存YOLO标注文件
            annotation_file = labels_dir / f"{image_name}.txt"
            write_yolo_detection_annotation(yolo_annotations, str(annotation_file))
            
            # 处理图像
            if extract_images:
                # 从JSON中提取图像
                json_file = labelme_dir / f"{image_name}.json"
                extracted_path = extract_image_from_labelme(str(json_file), str(images_dir))
                if extracted_path:
                    converted_images.append(os.path.basename(extracted_path))
            else:
                # 复制现有图像文件
                for ext in ['.jpg', '.jpeg', '.png', '.bmp']:
                    src_image = labelme_dir / f"{image_name}{ext}"
                    if src_image.exists():
                        dst_image = images_dir / f"{image_name}{ext}"
                        shutil.copy2(src_image, dst_image)
                        converted_images.append(f"{image_name}{ext}")
                        break
    
    # 创建数据集YAML配置
    yaml_config = create_yolo_dataset_yaml(
        classes=class_names,
        train_path='images',
        val_path='images'
    )
    
    with open(output_dir / 'data.yaml', 'w', encoding='utf-8') as f:
        yaml.dump(yaml_config, f, default_flow_style=False, allow_unicode=True)
    
    print(f"转换完成！输出保存到: {output_dir}")
    print(f"转换了 {len(valid_annotations)} 张图像")


def convert_labelme_to_yolo_segmentation(labelme_dir: str, output_dir: str,
                                        extract_images: bool = False) -> None:
    """将LabelMe分割数据集转换为YOLO格式
    
    Args:
        labelme_dir: LabelMe数据集目录
        output_dir: 输出YOLO数据集目录
        extract_images: 是否从JSON文件中提取图像
    """
    labelme_dir = Path(labelme_dir)
    output_dir = Path(output_dir)
    
    # 创建YOLO目录结构
    images_dir = output_dir / 'images'
    labels_dir = output_dir / 'labels'
    ensure_dir(images_dir)
    ensure_dir(labels_dir)
    
    # 查找所有JSON标注文件
    json_files = list(labelme_dir.glob('*.json'))
    if not json_files:
        raise ValueError(f"在目录中未找到JSON标注文件: {labelme_dir}")
    
    print(f"找到 {len(json_files)} 个标注文件")
    
    # 初始化数据集
    labelme_dataset = LabelMeDataset(str(labelme_dir))
    
    # 处理所有标注文件
    all_classes = set()
    valid_annotations = {}
    
    print("处理标注文件...")
    for json_file in tqdm(json_files):
        image_info, annotations = labelme_dataset.read_annotation(str(json_file))
        if image_info and annotations:
            # 过滤分割标注
            segmentation_annotations = [ann for ann in annotations 
                                      if ann.get('type') == 'segmentation' and 'polygon' in ann]
            if segmentation_annotations:
                valid_annotations[json_file.stem] = (image_info, segmentation_annotations)
                for ann in segmentation_annotations:
                    all_classes.add(ann['label'])
    
    if not valid_annotations:
        print("未找到有效的分割标注")
        return
    
    # 创建类别映射
    class_names = sorted(list(all_classes))
    class_to_id = {name: idx for idx, name in enumerate(class_names)}
    
    # 保存类别文件
    with open(output_dir / 'classes.txt', 'w', encoding='utf-8') as f:
        for class_name in class_names:
            f.write(f"{class_name}\n")
    
    print(f"找到 {len(class_names)} 个类别: {class_names}")
    
    # 转换标注
    print("转换标注...")
    converted_images = []
    
    for image_name, (image_info, annotations) in tqdm(valid_annotations.items()):
        filename = image_info['filename']
        img_width = image_info['width']
        img_height = image_info['height']
        
        if img_width == 0 or img_height == 0:
            print(f"跳过无效图像尺寸: {filename}")
            continue
        
        # 转换标注为YOLO格式
        yolo_annotations = []
        
        for ann in annotations:
            label = ann['label']
            class_id = class_to_id[label]
            polygon = ann['polygon']  # [x1, y1, x2, y2, ...]
            
            # 归一化多边形坐标
            normalized_polygon = []
            for i in range(0, len(polygon), 2):
                if i + 1 < len(polygon):
                    x = polygon[i] / img_width
                    y = polygon[i + 1] / img_height
                    normalized_polygon.extend([x, y])
            
            yolo_annotation = {
                'class_id': class_id,
                'polygon': normalized_polygon,
                'type': 'segmentation'
            }
            yolo_annotations.append(yolo_annotation)
        
        if yolo_annotations:
            # 保存YOLO标注文件
            annotation_file = labels_dir / f"{image_name}.txt"
            write_yolo_segmentation_annotation(yolo_annotations, str(annotation_file))
            
            # 处理图像
            if extract_images:
                # 从JSON中提取图像
                json_file = labelme_dir / f"{image_name}.json"
                extracted_path = extract_image_from_labelme(str(json_file), str(images_dir))
                if extracted_path:
                    converted_images.append(os.path.basename(extracted_path))
            else:
                # 复制现有图像文件
                for ext in ['.jpg', '.jpeg', '.png', '.bmp']:
                    src_image = labelme_dir / f"{image_name}{ext}"
                    if src_image.exists():
                        dst_image = images_dir / f"{image_name}{ext}"
                        shutil.copy2(src_image, dst_image)
                        converted_images.append(f"{image_name}{ext}")
                        break
    
    # 创建数据集YAML配置
    yaml_config = create_yolo_dataset_yaml(
        classes=class_names,
        train_path='images',
        val_path='images'
    )
    
    with open(output_dir / 'data.yaml', 'w', encoding='utf-8') as f:
        yaml.dump(yaml_config, f, default_flow_style=False, allow_unicode=True)
    
    print(f"转换完成！输出保存到: {output_dir}")
    print(f"转换了 {len(valid_annotations)} 张图像")


def convert_labelme_mixed_to_yolo(labelme_dir: str, output_dir: str,
                                 extract_images: bool = False,
                                 prefer_segmentation: bool = True) -> None:
    """将包含混合标注类型的LabelMe数据集转换为YOLO格式
    
    Args:
        labelme_dir: LabelMe数据集目录
        output_dir: 输出YOLO数据集目录
        extract_images: 是否从JSON文件中提取图像
        prefer_segmentation: 当同时存在检测和分割标注时，优先选择分割
    """
    labelme_dir = Path(labelme_dir)
    output_dir = Path(output_dir)
    
    # 创建YOLO目录结构
    images_dir = output_dir / 'images'
    labels_dir = output_dir / 'labels'
    ensure_dir(images_dir)
    ensure_dir(labels_dir)
    
    # 查找所有JSON标注文件
    json_files = list(labelme_dir.glob('*.json'))
    if not json_files:
        raise ValueError(f"在目录中未找到JSON标注文件: {labelme_dir}")
    
    print(f"找到 {len(json_files)} 个标注文件")
    
    # 初始化数据集
    labelme_dataset = LabelMeDataset(str(labelme_dir))
    
    # 处理所有标注文件
    all_classes = set()
    valid_annotations = {}
    
    print("处理标注文件...")
    for json_file in tqdm(json_files):
        image_info, annotations = labelme_dataset.read_annotation(str(json_file))
        if image_info and annotations:
            # 包含检测和分割标注
            valid_anns = [ann for ann in annotations 
                         if ann.get('type') in ['detection', 'segmentation']]
            if valid_anns:
                valid_annotations[json_file.stem] = (image_info, valid_anns)
                for ann in valid_anns:
                    all_classes.add(ann['label'])
    
    if not valid_annotations:
        print("未找到有效的标注")
        return
    
    # 创建类别映射
    class_names = sorted(list(all_classes))
    class_to_id = {name: idx for idx, name in enumerate(class_names)}
    
    # 保存类别文件
    with open(output_dir / 'classes.txt', 'w', encoding='utf-8') as f:
        for class_name in class_names:
            f.write(f"{class_name}\n")
    
    print(f"找到 {len(class_names)} 个类别: {class_names}")
    
    # 转换标注
    print("转换标注...")
    converted_images = []
    
    for image_name, (image_info, annotations) in tqdm(valid_annotations.items()):
        filename = image_info['filename']
        img_width = image_info['width']
        img_height = image_info['height']
        
        if img_width == 0 or img_height == 0:
            print(f"跳过无效图像尺寸: {filename}")
            continue
        
        # 根据优先级选择标注类型
        if prefer_segmentation:
            # 优先使用分割标注
            seg_annotations = [ann for ann in annotations 
                             if ann.get('type') == 'segmentation' and 'polygon' in ann]
            det_annotations = [ann for ann in annotations 
                             if ann.get('type') == 'detection' and 'bbox' in ann]
            
            # 如果有分割标注，使用分割格式
            if seg_annotations:
                yolo_annotations = []
                for ann in seg_annotations:
                    label = ann['label']
                    class_id = class_to_id[label]
                    polygon = ann['polygon']
                    
                    # 归一化多边形坐标
                    normalized_polygon = []
                    for i in range(0, len(polygon), 2):
                        if i + 1 < len(polygon):
                            x = polygon[i] / img_width
                            y = polygon[i + 1] / img_height
                            normalized_polygon.extend([x, y])
                    
                    yolo_annotation = {
                        'class_id': class_id,
                        'polygon': normalized_polygon,
                        'type': 'segmentation'
                    }
                    yolo_annotations.append(yolo_annotation)
                
                # 保存分割标注
                annotation_file = labels_dir / f"{image_name}.txt"
                write_yolo_segmentation_annotation(yolo_annotations, str(annotation_file))
                
            elif det_annotations:
                # 使用检测标注
                yolo_annotations = []
                for ann in det_annotations:
                    label = ann['label']
                    class_id = class_to_id[label]
                    bbox = ann['bbox']
                    
                    # 转换为YOLO格式
                    x_center = (bbox[0] + bbox[2] / 2) / img_width
                    y_center = (bbox[1] + bbox[3] / 2) / img_height
                    width = bbox[2] / img_width
                    height = bbox[3] / img_height
                    
                    yolo_annotation = {
                        'class_id': class_id,
                        'bbox': [x_center, y_center, width, height],
                        'type': 'detection'
                    }
                    yolo_annotations.append(yolo_annotation)
                
                # 保存检测标注
                annotation_file = labels_dir / f"{image_name}.txt"
                write_yolo_detection_annotation(yolo_annotations, str(annotation_file))
        
        # 处理图像
        if extract_images:
            # 从JSON中提取图像
            json_file = labelme_dir / f"{image_name}.json"
            extracted_path = extract_image_from_labelme(str(json_file), str(images_dir))
            if extracted_path:
                converted_images.append(os.path.basename(extracted_path))
        else:
            # 复制现有图像文件
            for ext in ['.jpg', '.jpeg', '.png', '.bmp']:
                src_image = labelme_dir / f"{image_name}{ext}"
                if src_image.exists():
                    dst_image = images_dir / f"{image_name}{ext}"
                    shutil.copy2(src_image, dst_image)
                    converted_images.append(f"{image_name}{ext}")
                    break
    
    # 创建数据集YAML配置
    yaml_config = create_yolo_dataset_yaml(
        classes=class_names,
        train_path='images',
        val_path='images'
    )
    
    with open(output_dir / 'data.yaml', 'w', encoding='utf-8') as f:
        yaml.dump(yaml_config, f, default_flow_style=False, allow_unicode=True)
    
    print(f"转换完成！输出保存到: {output_dir}")
    print(f"转换了 {len(valid_annotations)} 张图像")


def main():
    parser = argparse.ArgumentParser(description='将LabelMe数据集转换为YOLO格式')
    parser.add_argument('--labelme_dir', required=True, help='LabelMe数据集目录路径')
    parser.add_argument('--output_dir', required=True, help='输出YOLO数据集目录路径')
    parser.add_argument('--task', choices=['detection', 'segmentation', 'mixed'], 
                       default='mixed', help='任务类型')
    parser.add_argument('--extract_images', action='store_true',
                       help='从JSON文件中提取图像数据')
    parser.add_argument('--prefer_segmentation', action='store_true', default=True,
                       help='混合模式下优先使用分割标注')
    
    args = parser.parse_args()
    
    # 根据任务类型进行转换
    if args.task == 'detection':
        convert_labelme_to_yolo_detection(args.labelme_dir, args.output_dir, args.extract_images)
    elif args.task == 'segmentation':
        convert_labelme_to_yolo_segmentation(args.labelme_dir, args.output_dir, args.extract_images)
    elif args.task == 'mixed':
        convert_labelme_mixed_to_yolo(
            args.labelme_dir, args.output_dir, 
            args.extract_images, args.prefer_segmentation
        )


if __name__ == '__main__':
    main()
