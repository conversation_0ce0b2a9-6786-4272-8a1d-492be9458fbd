#!/usr/bin/env python3
"""
Convert Pascal VOC format dataset to COCO format
Supports: Object Detection, Instance Segmentation
"""
import os
import argparse
from pathlib import Path
from typing import Dict, List, Optional
from tqdm import tqdm

import sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.coco_utils import COCODataset
from utils.voc_utils import VOCDataset, convert_voc_bbox_to_coco
from utils.common import ensure_dir, get_image_size, polygon_to_bbox


def convert_voc_to_coco(voc_dir: str, output_file: str, task: str = 'detection') -> None:
    """Convert Pascal VOC dataset to COCO format"""
    voc_dir = Path(voc_dir)
    
    # Initialize VOC dataset
    voc_dataset = VOCDataset(str(voc_dir))
    
    # Create COCO dataset
    coco_dataset = COCODataset()
    
    # Find all XML annotation files
    annotations_dir = voc_dir / 'Annotations'
    if not annotations_dir.exists():
        raise ValueError(f"Annotations directory not found: {annotations_dir}")
    
    xml_files = list(annotations_dir.glob('*.xml'))
    if not xml_files:
        raise ValueError(f"No XML annotation files found in: {annotations_dir}")
    
    print(f"Found {len(xml_files)} annotation files")
    
    # Process all annotations to collect classes
    all_classes = set()
    image_annotations = {}
    
    print("Collecting class information...")
    for xml_file in tqdm(xml_files):
        image_info, annotations = voc_dataset.read_annotation_xml(str(xml_file))
        if image_info and annotations:
            image_annotations[xml_file.stem] = (image_info, annotations)
            for ann in annotations:
                all_classes.add(ann['class_name'])
    
    # Add categories to COCO dataset
    class_to_id = {}
    for idx, class_name in enumerate(sorted(all_classes)):
        category_id = idx + 1  # COCO categories start from 1
        coco_dataset.add_category(category_id, class_name)
        class_to_id[class_name] = category_id
    
    print(f"Found {len(all_classes)} classes: {sorted(all_classes)}")
    
    # Convert annotations
    print("Converting annotations...")
    image_id = 1
    annotation_id = 1
    
    for image_name, (image_info, annotations) in tqdm(image_annotations.items()):
        filename = image_info['filename']
        img_width = image_info['width']
        img_height = image_info['height']
        
        # Add image to COCO dataset
        coco_dataset.add_image(image_id, filename, img_width, img_height)
        
        # Convert annotations
        for ann in annotations:
            class_name = ann['class_name']
            category_id = class_to_id[class_name]
            
            if task == 'detection' or ann.get('type') == 'detection':
                # Convert VOC bbox [xmin, ymin, xmax, ymax] to COCO bbox [x, y, width, height]
                voc_bbox = ann['bbox']
                coco_bbox = convert_voc_bbox_to_coco(voc_bbox)
                
                # Add detection annotation
                coco_dataset.add_detection_annotation(
                    annotation_id, image_id, category_id, coco_bbox,
                    iscrowd=ann.get('difficult', 0)
                )
                
            elif task == 'segmentation' and ann.get('type') == 'segmentation':
                # Handle segmentation
                if 'polygon' in ann:
                    polygon = ann['polygon']
                    bbox = polygon_to_bbox(polygon)
                    
                    # Add segmentation annotation
                    coco_dataset.add_segmentation_annotation(
                        annotation_id, image_id, category_id, bbox, [polygon],
                        iscrowd=ann.get('difficult', 0)
                    )
                else:
                    # Fall back to bounding box
                    voc_bbox = ann['bbox']
                    coco_bbox = convert_voc_bbox_to_coco(voc_bbox)
                    
                    coco_dataset.add_detection_annotation(
                        annotation_id, image_id, category_id, coco_bbox,
                        iscrowd=ann.get('difficult', 0)
                    )
            
            annotation_id += 1
        
        image_id += 1
    
    # Save COCO dataset
    ensure_dir(os.path.dirname(output_file))
    coco_dataset.save_annotations(output_file)
    print(f"Conversion completed! Output saved to: {output_file}")
    print(f"Converted {len(image_annotations)} images with {annotation_id - 1} annotations")


def convert_voc_imageset_to_coco(voc_dir: str, imageset_file: str, output_file: str, 
                                task: str = 'detection') -> None:
    """Convert Pascal VOC dataset using ImageSet file to COCO format"""
    voc_dir = Path(voc_dir)
    
    # Read ImageSet file
    if not os.path.exists(imageset_file):
        raise ValueError(f"ImageSet file not found: {imageset_file}")
    
    with open(imageset_file, 'r', encoding='utf-8') as f:
        image_names = [line.strip() for line in f.readlines() if line.strip()]
    
    print(f"Found {len(image_names)} images in ImageSet file")
    
    # Initialize VOC dataset
    voc_dataset = VOCDataset(str(voc_dir))
    
    # Create COCO dataset
    coco_dataset = COCODataset()
    
    # Process annotations to collect classes
    all_classes = set()
    valid_annotations = {}
    
    annotations_dir = voc_dir / 'Annotations'
    
    print("Processing annotations...")
    for image_name in tqdm(image_names):
        xml_file = annotations_dir / f"{image_name}.xml"
        if xml_file.exists():
            image_info, annotations = voc_dataset.read_annotation_xml(str(xml_file))
            if image_info and annotations:
                valid_annotations[image_name] = (image_info, annotations)
                for ann in annotations:
                    all_classes.add(ann['class_name'])
    
    # Add categories to COCO dataset
    class_to_id = {}
    for idx, class_name in enumerate(sorted(all_classes)):
        category_id = idx + 1
        coco_dataset.add_category(category_id, class_name)
        class_to_id[class_name] = category_id
    
    print(f"Found {len(all_classes)} classes: {sorted(all_classes)}")
    
    # Convert annotations
    print("Converting annotations...")
    image_id = 1
    annotation_id = 1
    
    for image_name, (image_info, annotations) in tqdm(valid_annotations.items()):
        filename = image_info['filename']
        img_width = image_info['width']
        img_height = image_info['height']
        
        # Add image to COCO dataset
        coco_dataset.add_image(image_id, filename, img_width, img_height)
        
        # Convert annotations
        for ann in annotations:
            class_name = ann['class_name']
            category_id = class_to_id[class_name]
            
            if task == 'detection' or ann.get('type') == 'detection':
                # Convert bbox
                voc_bbox = ann['bbox']
                coco_bbox = convert_voc_bbox_to_coco(voc_bbox)
                
                coco_dataset.add_detection_annotation(
                    annotation_id, image_id, category_id, coco_bbox,
                    iscrowd=ann.get('difficult', 0)
                )
                
            elif task == 'segmentation' and ann.get('type') == 'segmentation':
                if 'polygon' in ann:
                    polygon = ann['polygon']
                    bbox = polygon_to_bbox(polygon)
                    
                    coco_dataset.add_segmentation_annotation(
                        annotation_id, image_id, category_id, bbox, [polygon],
                        iscrowd=ann.get('difficult', 0)
                    )
                else:
                    voc_bbox = ann['bbox']
                    coco_bbox = convert_voc_bbox_to_coco(voc_bbox)
                    
                    coco_dataset.add_detection_annotation(
                        annotation_id, image_id, category_id, coco_bbox,
                        iscrowd=ann.get('difficult', 0)
                    )
            
            annotation_id += 1
        
        image_id += 1
    
    # Save COCO dataset
    ensure_dir(os.path.dirname(output_file))
    coco_dataset.save_annotations(output_file)
    print(f"Conversion completed! Output saved to: {output_file}")
    print(f"Converted {len(valid_annotations)} images with {annotation_id - 1} annotations")


def main():
    parser = argparse.ArgumentParser(description='Convert Pascal VOC dataset to COCO format')
    parser.add_argument('--voc_dir', required=True, help='Path to Pascal VOC dataset directory')
    parser.add_argument('--output_file', required=True, help='Output COCO JSON file')
    parser.add_argument('--task', choices=['detection', 'segmentation'], 
                       default='detection', help='Task type')
    parser.add_argument('--imageset_file', help='Path to ImageSet file (e.g., train.txt)')
    
    args = parser.parse_args()
    
    # Convert based on whether ImageSet file is provided
    if args.imageset_file:
        convert_voc_imageset_to_coco(args.voc_dir, args.imageset_file, args.output_file, args.task)
    else:
        convert_voc_to_coco(args.voc_dir, args.output_file, args.task)


if __name__ == '__main__':
    main()
