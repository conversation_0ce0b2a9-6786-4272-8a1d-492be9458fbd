#!/usr/bin/env python3
"""
Convert Pascal VOC format dataset to YOLO format
Supports: Object Detection, Instance Segmentation
"""
import os
import argparse
import shutil
from pathlib import Path
from typing import Dict, List, Optional
from tqdm import tqdm
import yaml

import sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.voc_utils import VOCDataset, convert_voc_bbox_to_yolo
from utils.yolo_utils import (
    YOLODataset, write_yolo_detection_annotation, write_yolo_segmentation_annotation,
    create_yolo_dataset_yaml
)
from utils.common import ensure_dir, get_image_size


def convert_voc_to_yolo_detection(voc_dir: str, output_dir: str, 
                                 copy_images_flag: bool = True) -> None:
    """Convert Pascal VOC detection dataset to YOLO format"""
    voc_dir = Path(voc_dir)
    output_dir = Path(output_dir)
    
    # Create YOLO directory structure
    images_dir = output_dir / 'images'
    labels_dir = output_dir / 'labels'
    ensure_dir(images_dir)
    ensure_dir(labels_dir)
    
    # Initialize VOC dataset
    voc_dataset = VOCDataset(str(voc_dir))
    
    # Find all XML annotation files
    annotations_dir = voc_dir / 'Annotations'
    if not annotations_dir.exists():
        raise ValueError(f"Annotations directory not found: {annotations_dir}")
    
    xml_files = list(annotations_dir.glob('*.xml'))
    if not xml_files:
        raise ValueError(f"No XML annotation files found in: {annotations_dir}")
    
    print(f"Found {len(xml_files)} annotation files")
    
    # Process all annotations to collect classes
    all_classes = set()
    valid_annotations = {}
    
    print("Processing annotations...")
    for xml_file in tqdm(xml_files):
        image_info, annotations = voc_dataset.read_annotation_xml(str(xml_file))
        if image_info and annotations:
            valid_annotations[xml_file.stem] = (image_info, annotations)
            for ann in annotations:
                all_classes.add(ann['class_name'])
    
    # Create class mapping
    class_names = sorted(list(all_classes))
    class_to_id = {name: idx for idx, name in enumerate(class_names)}
    
    # Save classes file
    with open(output_dir / 'classes.txt', 'w', encoding='utf-8') as f:
        for class_name in class_names:
            f.write(f"{class_name}\n")
    
    print(f"Found {len(class_names)} classes: {class_names}")
    
    # Convert annotations
    print("Converting annotations...")
    converted_images = []
    
    for image_name, (image_info, annotations) in tqdm(valid_annotations.items()):
        filename = image_info['filename']
        img_width = image_info['width']
        img_height = image_info['height']
        
        # Convert annotations to YOLO format
        yolo_annotations = []
        
        for ann in annotations:
            if ann.get('type') in ['detection', 'segmentation']:
                # Convert VOC bbox to YOLO bbox
                voc_bbox = ann['bbox']  # [xmin, ymin, xmax, ymax]
                yolo_bbox = convert_voc_bbox_to_yolo(voc_bbox, img_width, img_height)
                
                # Get class ID
                class_name = ann['class_name']
                class_id = class_to_id[class_name]
                
                yolo_annotation = {
                    'class_id': class_id,
                    'bbox': yolo_bbox,
                    'type': 'detection'
                }
                yolo_annotations.append(yolo_annotation)
        
        if yolo_annotations:
            # Save YOLO annotation file
            annotation_file = labels_dir / f"{image_name}.txt"
            write_yolo_detection_annotation(yolo_annotations, str(annotation_file))
            
            converted_images.append(filename)
            
            # Copy image if requested
            if copy_images_flag:
                # Try to find image in JPEGImages directory
                src_image_path = voc_dir / 'JPEGImages' / filename
                if not src_image_path.exists():
                    # Try other common image directories
                    for img_dir in ['Images', 'images', 'JPEG']:
                        alt_path = voc_dir / img_dir / filename
                        if alt_path.exists():
                            src_image_path = alt_path
                            break
                
                if src_image_path.exists():
                    dst_image_path = images_dir / filename
                    shutil.copy2(src_image_path, dst_image_path)
    
    # Create dataset YAML
    yaml_config = create_yolo_dataset_yaml(
        classes=class_names,
        train_path='images',
        val_path='images'
    )
    
    with open(output_dir / 'data.yaml', 'w', encoding='utf-8') as f:
        yaml.dump(yaml_config, f, default_flow_style=False)
    
    print(f"Conversion completed! Output saved to: {output_dir}")
    print(f"Converted {len(converted_images)} images")


def convert_voc_to_yolo_segmentation(voc_dir: str, output_dir: str,
                                    copy_images_flag: bool = True) -> None:
    """Convert Pascal VOC segmentation dataset to YOLO format"""
    voc_dir = Path(voc_dir)
    output_dir = Path(output_dir)
    
    # Create YOLO directory structure
    images_dir = output_dir / 'images'
    labels_dir = output_dir / 'labels'
    ensure_dir(images_dir)
    ensure_dir(labels_dir)
    
    # Initialize VOC dataset
    voc_dataset = VOCDataset(str(voc_dir))
    
    # Find all XML annotation files
    annotations_dir = voc_dir / 'Annotations'
    if not annotations_dir.exists():
        raise ValueError(f"Annotations directory not found: {annotations_dir}")
    
    xml_files = list(annotations_dir.glob('*.xml'))
    if not xml_files:
        raise ValueError(f"No XML annotation files found in: {annotations_dir}")
    
    print(f"Found {len(xml_files)} annotation files")
    
    # Process all annotations to collect classes
    all_classes = set()
    valid_annotations = {}
    
    print("Processing annotations...")
    for xml_file in tqdm(xml_files):
        image_info, annotations = voc_dataset.read_annotation_xml(str(xml_file))
        if image_info and annotations:
            # Filter for segmentation annotations
            seg_annotations = [ann for ann in annotations if ann.get('type') == 'segmentation']
            if seg_annotations:
                valid_annotations[xml_file.stem] = (image_info, seg_annotations)
                for ann in seg_annotations:
                    all_classes.add(ann['class_name'])
    
    if not valid_annotations:
        print("No segmentation annotations found in the dataset")
        return
    
    # Create class mapping
    class_names = sorted(list(all_classes))
    class_to_id = {name: idx for idx, name in enumerate(class_names)}
    
    # Save classes file
    with open(output_dir / 'classes.txt', 'w', encoding='utf-8') as f:
        for class_name in class_names:
            f.write(f"{class_name}\n")
    
    print(f"Found {len(class_names)} classes: {class_names}")
    
    # Convert annotations
    print("Converting annotations...")
    converted_images = []
    
    for image_name, (image_info, annotations) in tqdm(valid_annotations.items()):
        filename = image_info['filename']
        img_width = image_info['width']
        img_height = image_info['height']
        
        # Convert annotations to YOLO format
        yolo_annotations = []
        
        for ann in annotations:
            if ann.get('type') == 'segmentation' and 'polygon' in ann:
                # Convert VOC polygon to YOLO polygon
                voc_polygon = ann['polygon']  # [x1, y1, x2, y2, ...]
                
                # Normalize polygon coordinates
                yolo_polygon = []
                for i in range(0, len(voc_polygon), 2):
                    if i + 1 < len(voc_polygon):
                        x = voc_polygon[i] / img_width
                        y = voc_polygon[i + 1] / img_height
                        yolo_polygon.extend([x, y])
                
                # Get class ID
                class_name = ann['class_name']
                class_id = class_to_id[class_name]
                
                yolo_annotation = {
                    'class_id': class_id,
                    'polygon': yolo_polygon,
                    'type': 'segmentation'
                }
                yolo_annotations.append(yolo_annotation)
        
        if yolo_annotations:
            # Save YOLO annotation file
            annotation_file = labels_dir / f"{image_name}.txt"
            write_yolo_segmentation_annotation(yolo_annotations, str(annotation_file))
            
            converted_images.append(filename)
            
            # Copy image if requested
            if copy_images_flag:
                # Try to find image in JPEGImages directory
                src_image_path = voc_dir / 'JPEGImages' / filename
                if not src_image_path.exists():
                    # Try other common image directories
                    for img_dir in ['Images', 'images', 'JPEG']:
                        alt_path = voc_dir / img_dir / filename
                        if alt_path.exists():
                            src_image_path = alt_path
                            break
                
                if src_image_path.exists():
                    dst_image_path = images_dir / filename
                    shutil.copy2(src_image_path, dst_image_path)
    
    # Create dataset YAML
    yaml_config = create_yolo_dataset_yaml(
        classes=class_names,
        train_path='images',
        val_path='images'
    )
    
    with open(output_dir / 'data.yaml', 'w', encoding='utf-8') as f:
        yaml.dump(yaml_config, f, default_flow_style=False)
    
    print(f"Conversion completed! Output saved to: {output_dir}")
    print(f"Converted {len(converted_images)} images")


def convert_voc_imageset_to_yolo(voc_dir: str, imageset_file: str, output_dir: str,
                                task: str = 'detection', copy_images_flag: bool = True) -> None:
    """Convert Pascal VOC dataset using ImageSet file to YOLO format"""
    voc_dir = Path(voc_dir)
    output_dir = Path(output_dir)
    
    # Read ImageSet file
    if not os.path.exists(imageset_file):
        raise ValueError(f"ImageSet file not found: {imageset_file}")
    
    with open(imageset_file, 'r', encoding='utf-8') as f:
        image_names = [line.strip() for line in f.readlines() if line.strip()]
    
    print(f"Found {len(image_names)} images in ImageSet file")
    
    # Create YOLO directory structure
    images_dir = output_dir / 'images'
    labels_dir = output_dir / 'labels'
    ensure_dir(images_dir)
    ensure_dir(labels_dir)
    
    # Initialize VOC dataset
    voc_dataset = VOCDataset(str(voc_dir))
    
    # Process annotations to collect classes
    all_classes = set()
    valid_annotations = {}
    
    annotations_dir = voc_dir / 'Annotations'
    
    print("Processing annotations...")
    for image_name in tqdm(image_names):
        xml_file = annotations_dir / f"{image_name}.xml"
        if xml_file.exists():
            image_info, annotations = voc_dataset.read_annotation_xml(str(xml_file))
            if image_info and annotations:
                if task == 'segmentation':
                    # Filter for segmentation annotations
                    annotations = [ann for ann in annotations if ann.get('type') == 'segmentation']
                
                if annotations:
                    valid_annotations[image_name] = (image_info, annotations)
                    for ann in annotations:
                        all_classes.add(ann['class_name'])
    
    if not valid_annotations:
        print(f"No valid annotations found for task: {task}")
        return
    
    # Create class mapping
    class_names = sorted(list(all_classes))
    class_to_id = {name: idx for idx, name in enumerate(class_names)}
    
    # Save classes file
    with open(output_dir / 'classes.txt', 'w', encoding='utf-8') as f:
        for class_name in class_names:
            f.write(f"{class_name}\n")
    
    print(f"Found {len(class_names)} classes: {class_names}")
    
    # Convert annotations
    print("Converting annotations...")
    converted_images = []
    
    for image_name, (image_info, annotations) in tqdm(valid_annotations.items()):
        filename = image_info['filename']
        img_width = image_info['width']
        img_height = image_info['height']
        
        # Convert annotations to YOLO format
        yolo_annotations = []
        
        for ann in annotations:
            class_name = ann['class_name']
            class_id = class_to_id[class_name]
            
            if task == 'detection':
                # Convert VOC bbox to YOLO bbox
                voc_bbox = ann['bbox']
                yolo_bbox = convert_voc_bbox_to_yolo(voc_bbox, img_width, img_height)
                
                yolo_annotation = {
                    'class_id': class_id,
                    'bbox': yolo_bbox,
                    'type': 'detection'
                }
                yolo_annotations.append(yolo_annotation)
                
            elif task == 'segmentation' and 'polygon' in ann:
                # Convert VOC polygon to YOLO polygon
                voc_polygon = ann['polygon']
                
                # Normalize polygon coordinates
                yolo_polygon = []
                for i in range(0, len(voc_polygon), 2):
                    if i + 1 < len(voc_polygon):
                        x = voc_polygon[i] / img_width
                        y = voc_polygon[i + 1] / img_height
                        yolo_polygon.extend([x, y])
                
                yolo_annotation = {
                    'class_id': class_id,
                    'polygon': yolo_polygon,
                    'type': 'segmentation'
                }
                yolo_annotations.append(yolo_annotation)
        
        if yolo_annotations:
            # Save YOLO annotation file
            annotation_file = labels_dir / f"{image_name}.txt"
            if task == 'detection':
                write_yolo_detection_annotation(yolo_annotations, str(annotation_file))
            elif task == 'segmentation':
                write_yolo_segmentation_annotation(yolo_annotations, str(annotation_file))
            
            converted_images.append(filename)
            
            # Copy image if requested
            if copy_images_flag:
                src_image_path = voc_dir / 'JPEGImages' / filename
                if not src_image_path.exists():
                    for img_dir in ['Images', 'images', 'JPEG']:
                        alt_path = voc_dir / img_dir / filename
                        if alt_path.exists():
                            src_image_path = alt_path
                            break
                
                if src_image_path.exists():
                    dst_image_path = images_dir / filename
                    shutil.copy2(src_image_path, dst_image_path)
    
    # Create dataset YAML
    yaml_config = create_yolo_dataset_yaml(
        classes=class_names,
        train_path='images',
        val_path='images'
    )
    
    with open(output_dir / 'data.yaml', 'w', encoding='utf-8') as f:
        yaml.dump(yaml_config, f, default_flow_style=False)
    
    print(f"Conversion completed! Output saved to: {output_dir}")
    print(f"Converted {len(converted_images)} images")


def main():
    parser = argparse.ArgumentParser(description='Convert Pascal VOC dataset to YOLO format')
    parser.add_argument('--voc_dir', required=True, help='Path to Pascal VOC dataset directory')
    parser.add_argument('--output_dir', required=True, help='Output directory for YOLO dataset')
    parser.add_argument('--task', choices=['detection', 'segmentation'], 
                       default='detection', help='Task type')
    parser.add_argument('--imageset_file', help='Path to ImageSet file (e.g., train.txt)')
    parser.add_argument('--copy_images', action='store_true', 
                       help='Copy images to output directory')
    
    args = parser.parse_args()
    
    # Convert based on whether ImageSet file is provided
    if args.imageset_file:
        convert_voc_imageset_to_yolo(args.voc_dir, args.imageset_file, args.output_dir, 
                                    args.task, args.copy_images)
    else:
        if args.task == 'detection':
            convert_voc_to_yolo_detection(args.voc_dir, args.output_dir, args.copy_images)
        elif args.task == 'segmentation':
            convert_voc_to_yolo_segmentation(args.voc_dir, args.output_dir, args.copy_images)


if __name__ == '__main__':
    main()
