#!/usr/bin/env python3
"""
Convert YOLO classification format dataset to ImageNet format
Supports: Image Classification
"""
import os
import argparse
import shutil
from pathlib import Path
from typing import Dict, List, Optional
from tqdm import tqdm

import sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.common import ensure_dir, get_image_files


def convert_yolo_cls_to_imagenet(yolo_dir: str, output_dir: str,
                                copy_images_flag: bool = True,
                                merge_splits: bool = True) -> None:
    """Convert YOLO classification format dataset to ImageNet format"""
    yolo_dir = Path(yolo_dir)
    output_dir = Path(output_dir)
    
    ensure_dir(output_dir)
    
    # Find split directories (train, val, test)
    split_dirs = []
    for split_name in ['train', 'val', 'test']:
        split_dir = yolo_dir / split_name
        if split_dir.exists() and split_dir.is_dir():
            split_dirs.append((split_name, split_dir))
    
    if not split_dirs:
        raise ValueError(f"No train/val/test directories found in: {yolo_dir}")
    
    print(f"Found splits: {[name for name, _ in split_dirs]}")
    
    # Collect all classes from all splits
    all_classes = set()
    split_class_data = {}
    
    for split_name, split_dir in split_dirs:
        class_dirs = [d for d in split_dir.iterdir() if d.is_dir()]
        split_classes = set(d.name for d in class_dirs)
        all_classes.update(split_classes)
        split_class_data[split_name] = {d.name: d for d in class_dirs}
        print(f"Split '{split_name}': {len(split_classes)} classes")
    
    all_classes = sorted(list(all_classes))
    print(f"Total unique classes: {len(all_classes)}")
    
    if merge_splits:
        # Merge all splits into single ImageNet structure
        print("Merging all splits into single ImageNet structure...")
        
        # Create class directories in output
        for class_name in all_classes:
            class_output_dir = output_dir / class_name
            ensure_dir(class_output_dir)
        
        # Process each split and class
        total_images = 0
        class_image_counts = {}
        
        for class_name in tqdm(all_classes, desc="Processing classes"):
            class_image_counts[class_name] = 0
            
            for split_name, split_data in split_class_data.items():
                if class_name in split_data:
                    class_dir = split_data[class_name]
                    image_files = get_image_files(class_dir)
                    
                    class_image_counts[class_name] += len(image_files)
                    total_images += len(image_files)
                    
                    if copy_images_flag:
                        class_output_dir = output_dir / class_name
                        
                        for img_file in image_files:
                            # Add split prefix to avoid name conflicts
                            new_name = f"{split_name}_{img_file.name}"
                            dst_path = class_output_dir / new_name
                            shutil.copy2(img_file, dst_path)
        
        # Create annotation file
        annotation_file = output_dir / 'annotations.txt'
        with open(annotation_file, 'w', encoding='utf-8') as f:
            for class_name in all_classes:
                class_dir = output_dir / class_name
                if class_dir.exists():
                    image_files = get_image_files(class_dir)
                    for img_file in image_files:
                        f.write(f"{img_file.name} {class_name}\n")
        
        print(f"Created merged ImageNet dataset with {total_images} images")
        print(f"Class distribution: {class_image_counts}")
        
    else:
        # Keep splits separate
        print("Keeping splits separate...")
        
        for split_name, split_dir in split_dirs:
            split_output_dir = output_dir / split_name
            ensure_dir(split_output_dir)
            
            split_classes = split_class_data[split_name]
            split_total_images = 0
            split_class_counts = {}
            
            for class_name in tqdm(split_classes.keys(), desc=f"Processing {split_name}"):
                class_dir = split_classes[class_name]
                image_files = get_image_files(class_dir)
                
                split_class_counts[class_name] = len(image_files)
                split_total_images += len(image_files)
                
                if copy_images_flag:
                    class_output_dir = split_output_dir / class_name
                    ensure_dir(class_output_dir)
                    
                    for img_file in image_files:
                        dst_path = class_output_dir / img_file.name
                        shutil.copy2(img_file, dst_path)
            
            # Create annotation file for this split
            annotation_file = split_output_dir / 'annotations.txt'
            with open(annotation_file, 'w', encoding='utf-8') as f:
                for class_name in split_classes.keys():
                    class_dir = split_output_dir / class_name
                    if class_dir.exists():
                        image_files = get_image_files(class_dir)
                        for img_file in image_files:
                            f.write(f"{img_file.name} {class_name}\n")
            
            print(f"Split '{split_name}': {split_total_images} images, {len(split_classes)} classes")
    
    # Save class names
    with open(output_dir / 'classes.txt', 'w', encoding='utf-8') as f:
        for class_name in all_classes:
            f.write(f"{class_name}\n")
    
    print(f"\nConversion completed! Output saved to: {output_dir}")


def convert_yolo_cls_to_imagenet_flat(yolo_dir: str, output_dir: str,
                                     copy_images_flag: bool = True) -> None:
    """Convert YOLO classification to flat ImageNet structure (all images in one directory)"""
    yolo_dir = Path(yolo_dir)
    output_dir = Path(output_dir)
    
    # Create output directories
    images_dir = output_dir / 'images'
    ensure_dir(images_dir)
    
    # Find split directories
    split_dirs = []
    for split_name in ['train', 'val', 'test']:
        split_dir = yolo_dir / split_name
        if split_dir.exists() and split_dir.is_dir():
            split_dirs.append((split_name, split_dir))
    
    if not split_dirs:
        raise ValueError(f"No train/val/test directories found in: {yolo_dir}")
    
    # Collect all classes and images
    all_classes = set()
    all_images = []
    
    for split_name, split_dir in split_dirs:
        class_dirs = [d for d in split_dir.iterdir() if d.is_dir()]
        
        for class_dir in class_dirs:
            class_name = class_dir.name
            all_classes.add(class_name)
            
            image_files = get_image_files(class_dir)
            for img_file in image_files:
                all_images.append((img_file, class_name, split_name))
    
    all_classes = sorted(list(all_classes))
    print(f"Found {len(all_images)} images across {len(all_classes)} classes")
    
    # Copy images and create annotation file
    annotation_file = output_dir / 'annotations.txt'
    class_image_counts = {class_name: 0 for class_name in all_classes}
    
    with open(annotation_file, 'w', encoding='utf-8') as f:
        for img_file, class_name, split_name in tqdm(all_images, desc="Processing images"):
            # Create unique filename to avoid conflicts
            new_name = f"{split_name}_{class_name}_{img_file.name}"
            
            if copy_images_flag:
                dst_path = images_dir / new_name
                shutil.copy2(img_file, dst_path)
            
            # Write annotation
            f.write(f"{new_name} {class_name}\n")
            class_image_counts[class_name] += 1
    
    # Save class names
    with open(output_dir / 'classes.txt', 'w', encoding='utf-8') as f:
        for class_name in all_classes:
            f.write(f"{class_name}\n")
    
    # Create summary
    with open(output_dir / 'dataset_summary.txt', 'w', encoding='utf-8') as f:
        f.write(f"Total images: {len(all_images)}\n")
        f.write(f"Total classes: {len(all_classes)}\n\n")
        f.write("Class distribution:\n")
        for class_name, count in sorted(class_image_counts.items()):
            f.write(f"{class_name}: {count}\n")
    
    print(f"Conversion completed! Output saved to: {output_dir}")
    print(f"Total images: {len(all_images)}")
    print(f"Classes: {len(all_classes)}")


def main():
    parser = argparse.ArgumentParser(description='Convert YOLO classification dataset to ImageNet format')
    parser.add_argument('--yolo_dir', required=True, help='Path to YOLO classification dataset directory')
    parser.add_argument('--output_dir', required=True, help='Output directory for ImageNet dataset')
    parser.add_argument('--copy_images', action='store_true', 
                       help='Copy images to output directory')
    parser.add_argument('--merge_splits', action='store_true', 
                       help='Merge train/val/test splits into single structure')
    parser.add_argument('--flat_structure', action='store_true',
                       help='Create flat structure with all images in one directory')
    
    args = parser.parse_args()
    
    if args.flat_structure:
        convert_yolo_cls_to_imagenet_flat(args.yolo_dir, args.output_dir, args.copy_images)
    else:
        convert_yolo_cls_to_imagenet(args.yolo_dir, args.output_dir, args.copy_images, args.merge_splits)


if __name__ == '__main__':
    main()
