#!/usr/bin/env python3
"""
Convert YOLO format dataset to COCO format
Supports: Object Detection, Instance Segmentation, Keypoint Detection
"""
import os
import argparse
import json
from pathlib import Path
from typing import Dict, List, Optional
from tqdm import tqdm

import sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.coco_utils import COCODataset, create_coco_keypoint_categories
from utils.yolo_utils import (
    YOLODataset, read_yolo_detection_annotation, read_yolo_segmentation_annotation,
    read_yolo_pose_annotation
)
from utils.common import ensure_dir, get_image_size, get_image_files, polygon_to_bbox


def convert_yolo_to_coco_detection(yolo_dir: str, images_dir: str, output_file: str) -> None:
    """Convert YOLO detection dataset to COCO format"""
    yolo_dir = Path(yolo_dir)
    images_dir = Path(images_dir)
    
    # Load YOLO dataset
    yolo_dataset = YOLODataset(str(yolo_dir))
    
    # Create COCO dataset
    coco_dataset = COCODataset()
    
    # Add categories
    for idx, class_name in enumerate(yolo_dataset.classes):
        coco_dataset.add_category(idx + 1, class_name)
    
    # Get all image files
    image_files = get_image_files(images_dir)
    
    print(f"Converting {len(image_files)} images...")
    
    image_id = 1
    annotation_id = 1
    
    for image_file in tqdm(image_files):
        # Get image info
        img_width, img_height = get_image_size(image_file)
        if img_width == 0 or img_height == 0:
            continue
        
        filename = image_file.name
        
        # Add image to COCO dataset
        coco_dataset.add_image(image_id, filename, img_width, img_height)
        
        # Read YOLO annotations
        annotation_file = yolo_dir / 'labels' / f"{image_file.stem}.txt"
        annotations = read_yolo_detection_annotation(str(annotation_file))
        
        # Convert annotations
        for ann in annotations:
            if ann.get('type') == 'detection':
                # Convert YOLO bbox to COCO bbox
                yolo_bbox = ann['bbox']  # [x_center, y_center, width, height] normalized
                
                # Denormalize
                x_center = yolo_bbox[0] * img_width
                y_center = yolo_bbox[1] * img_height
                width = yolo_bbox[2] * img_width
                height = yolo_bbox[3] * img_height
                
                # Convert to COCO format [x, y, width, height]
                coco_bbox = [x_center - width/2, y_center - height/2, width, height]
                
                # Add annotation
                category_id = ann['class_id'] + 1  # COCO categories start from 1
                coco_dataset.add_detection_annotation(
                    annotation_id, image_id, category_id, coco_bbox
                )
                annotation_id += 1
        
        image_id += 1
    
    # Save COCO dataset
    ensure_dir(os.path.dirname(output_file))
    coco_dataset.save_annotations(output_file)
    print(f"Conversion completed! Output saved to: {output_file}")


def convert_yolo_to_coco_segmentation(yolo_dir: str, images_dir: str, output_file: str) -> None:
    """Convert YOLO segmentation dataset to COCO format"""
    yolo_dir = Path(yolo_dir)
    images_dir = Path(images_dir)
    
    # Load YOLO dataset
    yolo_dataset = YOLODataset(str(yolo_dir))
    
    # Create COCO dataset
    coco_dataset = COCODataset()
    
    # Add categories
    for idx, class_name in enumerate(yolo_dataset.classes):
        coco_dataset.add_category(idx + 1, class_name)
    
    # Get all image files
    image_files = get_image_files(images_dir)
    
    print(f"Converting {len(image_files)} images...")
    
    image_id = 1
    annotation_id = 1
    
    for image_file in tqdm(image_files):
        # Get image info
        img_width, img_height = get_image_size(image_file)
        if img_width == 0 or img_height == 0:
            continue
        
        filename = image_file.name
        
        # Add image to COCO dataset
        coco_dataset.add_image(image_id, filename, img_width, img_height)
        
        # Read YOLO annotations
        annotation_file = yolo_dir / 'labels' / f"{image_file.stem}.txt"
        annotations = read_yolo_segmentation_annotation(str(annotation_file))
        
        # Convert annotations
        for ann in annotations:
            if ann.get('type') == 'segmentation':
                # Convert YOLO polygon to COCO polygon
                yolo_polygon = ann['polygon']  # normalized coordinates
                
                # Denormalize polygon
                coco_polygon = []
                for i in range(0, len(yolo_polygon), 2):
                    if i + 1 < len(yolo_polygon):
                        x = yolo_polygon[i] * img_width
                        y = yolo_polygon[i + 1] * img_height
                        coco_polygon.extend([x, y])
                
                # Calculate bounding box from polygon
                bbox = polygon_to_bbox(coco_polygon)
                
                # Add annotation
                category_id = ann['class_id'] + 1
                coco_dataset.add_segmentation_annotation(
                    annotation_id, image_id, category_id, bbox, [coco_polygon]
                )
                annotation_id += 1
        
        image_id += 1
    
    # Save COCO dataset
    ensure_dir(os.path.dirname(output_file))
    coco_dataset.save_annotations(output_file)
    print(f"Conversion completed! Output saved to: {output_file}")


def convert_yolo_to_coco_pose(yolo_dir: str, images_dir: str, output_file: str) -> None:
    """Convert YOLO pose dataset to COCO format"""
    yolo_dir = Path(yolo_dir)
    images_dir = Path(images_dir)
    
    # Load YOLO dataset
    yolo_dataset = YOLODataset(str(yolo_dir))
    
    # Create COCO dataset
    coco_dataset = COCODataset()
    
    # Add keypoint categories (COCO person keypoints)
    keypoint_categories = create_coco_keypoint_categories()
    for cat in keypoint_categories:
        coco_dataset.categories.append(cat)
    
    # Get all image files
    image_files = get_image_files(images_dir)
    
    print(f"Converting {len(image_files)} images...")
    
    image_id = 1
    annotation_id = 1
    
    for image_file in tqdm(image_files):
        # Get image info
        img_width, img_height = get_image_size(image_file)
        if img_width == 0 or img_height == 0:
            continue
        
        filename = image_file.name
        
        # Add image to COCO dataset
        coco_dataset.add_image(image_id, filename, img_width, img_height)
        
        # Read YOLO annotations
        annotation_file = yolo_dir / 'labels' / f"{image_file.stem}.txt"
        annotations = read_yolo_pose_annotation(str(annotation_file))
        
        # Convert annotations
        for ann in annotations:
            if ann.get('type') == 'pose':
                # Convert YOLO bbox to COCO bbox
                yolo_bbox = ann['bbox']  # [x_center, y_center, width, height] normalized
                
                # Denormalize bbox
                x_center = yolo_bbox[0] * img_width
                y_center = yolo_bbox[1] * img_height
                width = yolo_bbox[2] * img_width
                height = yolo_bbox[3] * img_height
                
                # Convert to COCO format [x, y, width, height]
                coco_bbox = [x_center - width/2, y_center - height/2, width, height]
                
                # Convert YOLO keypoints to COCO keypoints
                yolo_keypoints = ann['keypoints']  # [x1, y1, v1, x2, y2, v2, ...] normalized
                coco_keypoints = []
                num_keypoints = 0
                
                for i in range(0, len(yolo_keypoints), 3):
                    if i + 2 < len(yolo_keypoints):
                        x = yolo_keypoints[i] * img_width if yolo_keypoints[i] > 0 else 0
                        y = yolo_keypoints[i + 1] * img_height if yolo_keypoints[i + 1] > 0 else 0
                        v = int(yolo_keypoints[i + 2])  # visibility flag
                        
                        coco_keypoints.extend([x, y, v])
                        if v > 0:
                            num_keypoints += 1
                
                # Add annotation
                category_id = 1  # person category
                coco_dataset.add_keypoint_annotation(
                    annotation_id, image_id, category_id, coco_bbox, 
                    coco_keypoints, num_keypoints
                )
                annotation_id += 1
        
        image_id += 1
    
    # Save COCO dataset
    ensure_dir(os.path.dirname(output_file))
    coco_dataset.save_annotations(output_file)
    print(f"Conversion completed! Output saved to: {output_file}")


def main():
    parser = argparse.ArgumentParser(description='Convert YOLO dataset to COCO format')
    parser.add_argument('--yolo_dir', required=True, help='Path to YOLO dataset directory')
    parser.add_argument('--images_dir', required=True, help='Path to images directory')
    parser.add_argument('--output_file', required=True, help='Output COCO JSON file')
    parser.add_argument('--task', choices=['detection', 'segmentation', 'pose'], 
                       default='detection', help='Task type')
    
    args = parser.parse_args()
    
    # Convert based on task type
    if args.task == 'detection':
        convert_yolo_to_coco_detection(args.yolo_dir, args.images_dir, args.output_file)
    elif args.task == 'segmentation':
        convert_yolo_to_coco_segmentation(args.yolo_dir, args.images_dir, args.output_file)
    elif args.task == 'pose':
        convert_yolo_to_coco_pose(args.yolo_dir, args.images_dir, args.output_file)


if __name__ == '__main__':
    main()
