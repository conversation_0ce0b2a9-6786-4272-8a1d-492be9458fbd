#!/usr/bin/env python3
"""
将YOLO格式数据集转换为LabelMe格式
支持：目标检测、实例分割
"""
import os
import argparse
import shutil
import base64
from pathlib import Path
from typing import Dict, List, Optional
from tqdm import tqdm

import sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.yolo_utils import (
    YOLODataset, read_yolo_detection_annotation, read_yolo_segmentation_annotation
)
from utils.labelme_utils import (
    LabelMeDataset, create_labelme_shape, bbox_to_labelme_shape,
    convert_coco_polygon_to_labelme
)
from utils.common import ensure_dir, get_image_files, get_image_size


def encode_image_to_base64(image_path: str) -> Optional[str]:
    """将图像编码为base64字符串"""
    try:
        with open(image_path, 'rb') as f:
            image_data = base64.b64encode(f.read()).decode('utf-8')
        return image_data
    except Exception as e:
        print(f"编码图像失败 {image_path}: {e}")
        return None


def convert_yolo_to_labelme_detection(yolo_dir: str, images_dir: str, output_dir: str,
                                     include_image_data: bool = False) -> None:
    """将YOLO检测数据集转换为LabelMe格式
    
    Args:
        yolo_dir: YOLO数据集目录
        images_dir: 图像目录
        output_dir: 输出LabelMe数据集目录
        include_image_data: 是否在JSON中包含图像数据
    """
    yolo_dir = Path(yolo_dir)
    images_dir = Path(images_dir)
    output_dir = Path(output_dir)
    
    # 创建输出目录
    ensure_dir(output_dir)
    
    # 加载YOLO数据集
    yolo_dataset = YOLODataset(str(yolo_dir))
    
    # 获取所有图像文件
    image_files = get_image_files(images_dir)
    
    print(f"转换 {len(image_files)} 张图像...")
    
    converted_count = 0
    
    for image_file in tqdm(image_files):
        # 获取图像信息
        img_width, img_height = get_image_size(image_file)
        if img_width == 0 or img_height == 0:
            continue
        
        filename = image_file.name
        image_stem = image_file.stem
        
        # 读取YOLO标注
        annotation_file = yolo_dir / 'labels' / f"{image_stem}.txt"
        annotations = read_yolo_detection_annotation(str(annotation_file))
        
        if not annotations:
            continue  # 跳过没有标注的图像
        
        # 准备LabelMe形状列表
        shapes = []
        
        for ann in annotations:
            if ann.get('type') == 'detection':
                class_id = ann['class_id']
                class_name = yolo_dataset.get_class_name(class_id)
                if class_name is None:
                    class_name = f"class_{class_id}"
                
                # 转换YOLO边界框到像素坐标
                yolo_bbox = ann['bbox']  # [x_center, y_center, width, height] 归一化
                
                # 反归一化
                x_center = yolo_bbox[0] * img_width
                y_center = yolo_bbox[1] * img_height
                width = yolo_bbox[2] * img_width
                height = yolo_bbox[3] * img_height
                
                # 转换为[x, y, width, height]格式
                x = x_center - width / 2
                y = y_center - height / 2
                bbox = [x, y, width, height]
                
                # 创建LabelMe矩形形状
                shape = bbox_to_labelme_shape(class_name, bbox, 'rectangle')
                shapes.append(shape)
        
        if shapes:
            # 准备图像数据
            image_data = None
            if include_image_data:
                image_data = encode_image_to_base64(str(image_file))
            
            # 创建LabelMe标注
            labelme_dataset = LabelMeDataset()
            annotation = labelme_dataset.create_annotation(
                filename, shapes, image_data
            )
            
            # 保存LabelMe标注文件
            json_filename = f"{image_stem}.json"
            output_json_path = output_dir / json_filename
            labelme_dataset.save_annotation(annotation, str(output_json_path))
            
            # 复制图像文件（如果不包含图像数据）
            if not include_image_data:
                output_image_path = output_dir / filename
                shutil.copy2(image_file, output_image_path)
            
            converted_count += 1
    
    print(f"转换完成！输出保存到: {output_dir}")
    print(f"成功转换 {converted_count} 张图像")
    print(f"类别: {yolo_dataset.classes}")


def convert_yolo_to_labelme_segmentation(yolo_dir: str, images_dir: str, output_dir: str,
                                        include_image_data: bool = False) -> None:
    """将YOLO分割数据集转换为LabelMe格式
    
    Args:
        yolo_dir: YOLO数据集目录
        images_dir: 图像目录
        output_dir: 输出LabelMe数据集目录
        include_image_data: 是否在JSON中包含图像数据
    """
    yolo_dir = Path(yolo_dir)
    images_dir = Path(images_dir)
    output_dir = Path(output_dir)
    
    # 创建输出目录
    ensure_dir(output_dir)
    
    # 加载YOLO数据集
    yolo_dataset = YOLODataset(str(yolo_dir))
    
    # 获取所有图像文件
    image_files = get_image_files(images_dir)
    
    print(f"转换 {len(image_files)} 张图像...")
    
    converted_count = 0
    
    for image_file in tqdm(image_files):
        # 获取图像信息
        img_width, img_height = get_image_size(image_file)
        if img_width == 0 or img_height == 0:
            continue
        
        filename = image_file.name
        image_stem = image_file.stem
        
        # 读取YOLO分割标注
        annotation_file = yolo_dir / 'labels' / f"{image_stem}.txt"
        annotations = read_yolo_segmentation_annotation(str(annotation_file))
        
        if not annotations:
            continue  # 跳过没有标注的图像
        
        # 准备LabelMe形状列表
        shapes = []
        
        for ann in annotations:
            if ann.get('type') == 'segmentation':
                class_id = ann['class_id']
                class_name = yolo_dataset.get_class_name(class_id)
                if class_name is None:
                    class_name = f"class_{class_id}"
                
                # 转换YOLO多边形到像素坐标
                yolo_polygon = ann['polygon']  # [x1, y1, x2, y2, ...] 归一化
                
                # 反归一化并转换为LabelMe点格式
                points = []
                for i in range(0, len(yolo_polygon), 2):
                    if i + 1 < len(yolo_polygon):
                        x = yolo_polygon[i] * img_width
                        y = yolo_polygon[i + 1] * img_height
                        points.append([x, y])
                
                # 创建LabelMe多边形形状
                shape = create_labelme_shape(class_name, 'polygon', points)
                shapes.append(shape)
        
        if shapes:
            # 准备图像数据
            image_data = None
            if include_image_data:
                image_data = encode_image_to_base64(str(image_file))
            
            # 创建LabelMe标注
            labelme_dataset = LabelMeDataset()
            annotation = labelme_dataset.create_annotation(
                filename, shapes, image_data
            )
            
            # 保存LabelMe标注文件
            json_filename = f"{image_stem}.json"
            output_json_path = output_dir / json_filename
            labelme_dataset.save_annotation(annotation, str(output_json_path))
            
            # 复制图像文件（如果不包含图像数据）
            if not include_image_data:
                output_image_path = output_dir / filename
                shutil.copy2(image_file, output_image_path)
            
            converted_count += 1
    
    print(f"转换完成！输出保存到: {output_dir}")
    print(f"成功转换 {converted_count} 张图像")
    print(f"类别: {yolo_dataset.classes}")


def convert_yolo_mixed_to_labelme(yolo_dir: str, images_dir: str, output_dir: str,
                                 include_image_data: bool = False) -> None:
    """将包含混合标注类型的YOLO数据集转换为LabelMe格式
    
    Args:
        yolo_dir: YOLO数据集目录
        images_dir: 图像目录
        output_dir: 输出LabelMe数据集目录
        include_image_data: 是否在JSON中包含图像数据
    """
    yolo_dir = Path(yolo_dir)
    images_dir = Path(images_dir)
    output_dir = Path(output_dir)
    
    # 创建输出目录
    ensure_dir(output_dir)
    
    # 加载YOLO数据集
    yolo_dataset = YOLODataset(str(yolo_dir))
    
    # 获取所有图像文件
    image_files = get_image_files(images_dir)
    
    print(f"转换 {len(image_files)} 张图像...")
    
    converted_count = 0
    
    for image_file in tqdm(image_files):
        # 获取图像信息
        img_width, img_height = get_image_size(image_file)
        if img_width == 0 or img_height == 0:
            continue
        
        filename = image_file.name
        image_stem = image_file.stem
        
        # 尝试读取不同类型的标注
        annotation_file = yolo_dir / 'labels' / f"{image_stem}.txt"
        
        # 首先尝试分割标注
        seg_annotations = read_yolo_segmentation_annotation(str(annotation_file))
        det_annotations = read_yolo_detection_annotation(str(annotation_file))
        
        # 准备LabelMe形状列表
        shapes = []
        
        # 处理分割标注
        for ann in seg_annotations:
            if ann.get('type') == 'segmentation':
                class_id = ann['class_id']
                class_name = yolo_dataset.get_class_name(class_id)
                if class_name is None:
                    class_name = f"class_{class_id}"
                
                # 转换多边形
                yolo_polygon = ann['polygon']
                points = []
                for i in range(0, len(yolo_polygon), 2):
                    if i + 1 < len(yolo_polygon):
                        x = yolo_polygon[i] * img_width
                        y = yolo_polygon[i + 1] * img_height
                        points.append([x, y])
                
                shape = create_labelme_shape(class_name, 'polygon', points)
                shapes.append(shape)
        
        # 如果没有分割标注，处理检测标注
        if not shapes:
            for ann in det_annotations:
                if ann.get('type') == 'detection':
                    class_id = ann['class_id']
                    class_name = yolo_dataset.get_class_name(class_id)
                    if class_name is None:
                        class_name = f"class_{class_id}"
                    
                    # 转换边界框
                    yolo_bbox = ann['bbox']
                    x_center = yolo_bbox[0] * img_width
                    y_center = yolo_bbox[1] * img_height
                    width = yolo_bbox[2] * img_width
                    height = yolo_bbox[3] * img_height
                    
                    x = x_center - width / 2
                    y = y_center - height / 2
                    bbox = [x, y, width, height]
                    
                    shape = bbox_to_labelme_shape(class_name, bbox, 'rectangle')
                    shapes.append(shape)
        
        if shapes:
            # 准备图像数据
            image_data = None
            if include_image_data:
                image_data = encode_image_to_base64(str(image_file))
            
            # 创建LabelMe标注
            labelme_dataset = LabelMeDataset()
            annotation = labelme_dataset.create_annotation(
                filename, shapes, image_data
            )
            
            # 保存LabelMe标注文件
            json_filename = f"{image_stem}.json"
            output_json_path = output_dir / json_filename
            labelme_dataset.save_annotation(annotation, str(output_json_path))
            
            # 复制图像文件（如果不包含图像数据）
            if not include_image_data:
                output_image_path = output_dir / filename
                shutil.copy2(image_file, output_image_path)
            
            converted_count += 1
    
    print(f"转换完成！输出保存到: {output_dir}")
    print(f"成功转换 {converted_count} 张图像")
    print(f"类别: {yolo_dataset.classes}")


def main():
    parser = argparse.ArgumentParser(description='将YOLO数据集转换为LabelMe格式')
    parser.add_argument('--yolo_dir', required=True, help='YOLO数据集目录路径')
    parser.add_argument('--images_dir', required=True, help='图像目录路径')
    parser.add_argument('--output_dir', required=True, help='输出LabelMe数据集目录路径')
    parser.add_argument('--task', choices=['detection', 'segmentation', 'mixed'], 
                       default='mixed', help='任务类型')
    parser.add_argument('--include_image_data', action='store_true',
                       help='在JSON文件中包含base64编码的图像数据')
    
    args = parser.parse_args()
    
    # 根据任务类型进行转换
    if args.task == 'detection':
        convert_yolo_to_labelme_detection(
            args.yolo_dir, args.images_dir, args.output_dir, args.include_image_data
        )
    elif args.task == 'segmentation':
        convert_yolo_to_labelme_segmentation(
            args.yolo_dir, args.images_dir, args.output_dir, args.include_image_data
        )
    elif args.task == 'mixed':
        convert_yolo_mixed_to_labelme(
            args.yolo_dir, args.images_dir, args.output_dir, args.include_image_data
        )


if __name__ == '__main__':
    main()
