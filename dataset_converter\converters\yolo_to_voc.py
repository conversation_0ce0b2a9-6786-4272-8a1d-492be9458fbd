#!/usr/bin/env python3
"""
Convert YOLO format dataset to Pascal VOC format
Supports: Object Detection, Instance Segmentation, OBB Detection
"""
import os
import argparse
import shutil
from pathlib import Path
from typing import Dict, List, Optional
from tqdm import tqdm

import sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.yolo_utils import (
    YOLODataset, read_yolo_detection_annotation, read_yolo_segmentation_annotation,
    read_yolo_obb_annotation
)
from utils.voc_utils import VOCDataset, create_voc_directory_structure, split_dataset
from utils.common import ensure_dir, get_image_size, get_image_files


def convert_yolo_to_voc_detection(yolo_dir: str, images_dir: str, output_dir: str,
                                 copy_images_flag: bool = True) -> None:
    """Convert YOLO detection dataset to Pascal VOC format"""
    yolo_dir = Path(yolo_dir)
    images_dir = Path(images_dir)
    
    # Load YOLO dataset
    yolo_dataset = YOLODataset(str(yolo_dir))
    
    # Create VOC directory structure
    voc_dirs = create_voc_directory_structure(output_dir)
    
    # Initialize VOC dataset
    voc_dataset = VOCDataset()
    
    # Get all image files
    image_files = get_image_files(images_dir)
    
    print(f"Converting {len(image_files)} images...")
    
    converted_images = []
    
    for image_file in tqdm(image_files):
        # Get image info
        img_width, img_height = get_image_size(image_file)
        if img_width == 0 or img_height == 0:
            continue
        
        filename = image_file.name
        
        # Read YOLO annotations
        annotation_file = yolo_dir / 'labels' / f"{image_file.stem}.txt"
        annotations = read_yolo_detection_annotation(str(annotation_file))
        
        if not annotations:
            continue  # Skip images without annotations
        
        # Convert annotations to VOC format
        voc_annotations = []
        
        for ann in annotations:
            if ann.get('type') == 'detection':
                # Convert YOLO bbox to VOC bbox
                yolo_bbox = ann['bbox']  # [x_center, y_center, width, height] normalized
                
                # Denormalize and convert to corner format
                x_center = yolo_bbox[0] * img_width
                y_center = yolo_bbox[1] * img_height
                width = yolo_bbox[2] * img_width
                height = yolo_bbox[3] * img_height
                
                xmin = x_center - width / 2
                ymin = y_center - height / 2
                xmax = x_center + width / 2
                ymax = y_center + height / 2
                
                # Get class name
                class_id = ann['class_id']
                class_name = yolo_dataset.get_class_name(class_id)
                if class_name is None:
                    class_name = f"class_{class_id}"
                
                voc_annotation = {
                    'class_name': class_name,
                    'bbox': [xmin, ymin, xmax, ymax],
                    'truncated': 0,
                    'difficult': 0,
                    'type': 'detection'
                }
                voc_annotations.append(voc_annotation)
        
        if voc_annotations:
            # Create image info for VOC
            voc_image_info = {
                'filename': filename,
                'width': img_width,
                'height': img_height,
                'depth': 3
            }
            
            # Create XML annotation file
            xml_filename = f"{image_file.stem}.xml"
            xml_path = os.path.join(voc_dirs['annotations'], xml_filename)
            voc_dataset.create_annotation_xml(voc_image_info, voc_annotations, xml_path)
            
            converted_images.append(filename)
            
            # Copy image if requested
            if copy_images_flag:
                dst_image_path = Path(voc_dirs['images']) / filename
                shutil.copy2(image_file, dst_image_path)
    
    # Create ImageSet files
    if converted_images:
        splits = split_dataset(converted_images, train_ratio=0.8, val_ratio=0.1, test_ratio=0.1)
        
        for split_name, image_list in splits.items():
            if image_list:
                imageset_file = os.path.join(voc_dirs['imagesets_main'], f"{split_name}.txt")
                voc_dataset.create_imageset_file(image_list, imageset_file)
        
        # Create combined trainval set
        trainval_images = splits['train'] + splits['val']
        if trainval_images:
            trainval_file = os.path.join(voc_dirs['imagesets_main'], "trainval.txt")
            voc_dataset.create_imageset_file(trainval_images, trainval_file)
    
    print(f"Conversion completed! Output saved to: {output_dir}")
    print(f"Converted {len(converted_images)} images")
    print(f"Classes: {yolo_dataset.classes}")


def convert_yolo_to_voc_segmentation(yolo_dir: str, images_dir: str, output_dir: str,
                                    copy_images_flag: bool = True) -> None:
    """Convert YOLO segmentation dataset to Pascal VOC format"""
    yolo_dir = Path(yolo_dir)
    images_dir = Path(images_dir)
    
    # Load YOLO dataset
    yolo_dataset = YOLODataset(str(yolo_dir))
    
    # Create VOC directory structure
    voc_dirs = create_voc_directory_structure(output_dir)
    
    # Initialize VOC dataset
    voc_dataset = VOCDataset()
    
    # Get all image files
    image_files = get_image_files(images_dir)
    
    print(f"Converting {len(image_files)} images...")
    
    converted_images = []
    
    for image_file in tqdm(image_files):
        # Get image info
        img_width, img_height = get_image_size(image_file)
        if img_width == 0 or img_height == 0:
            continue
        
        filename = image_file.name
        
        # Read YOLO annotations
        annotation_file = yolo_dir / 'labels' / f"{image_file.stem}.txt"
        annotations = read_yolo_segmentation_annotation(str(annotation_file))
        
        if not annotations:
            continue
        
        # Convert annotations to VOC format
        voc_annotations = []
        
        for ann in annotations:
            if ann.get('type') == 'segmentation':
                # Convert YOLO polygon to VOC polygon
                yolo_polygon = ann['polygon']  # normalized coordinates
                
                # Denormalize polygon
                voc_polygon = []
                for i in range(0, len(yolo_polygon), 2):
                    if i + 1 < len(yolo_polygon):
                        x = yolo_polygon[i] * img_width
                        y = yolo_polygon[i + 1] * img_height
                        voc_polygon.extend([x, y])
                
                # Calculate bounding box from polygon
                if len(voc_polygon) >= 6:  # At least 3 points
                    x_coords = voc_polygon[0::2]
                    y_coords = voc_polygon[1::2]
                    
                    xmin, xmax = min(x_coords), max(x_coords)
                    ymin, ymax = min(y_coords), max(y_coords)
                    
                    # Get class name
                    class_id = ann['class_id']
                    class_name = yolo_dataset.get_class_name(class_id)
                    if class_name is None:
                        class_name = f"class_{class_id}"
                    
                    voc_annotation = {
                        'class_name': class_name,
                        'bbox': [xmin, ymin, xmax, ymax],
                        'polygon': voc_polygon,
                        'truncated': 0,
                        'difficult': 0,
                        'type': 'segmentation'
                    }
                    voc_annotations.append(voc_annotation)
        
        if voc_annotations:
            # Create image info for VOC
            voc_image_info = {
                'filename': filename,
                'width': img_width,
                'height': img_height,
                'depth': 3
            }
            
            # Create XML annotation file
            xml_filename = f"{image_file.stem}.xml"
            xml_path = os.path.join(voc_dirs['annotations'], xml_filename)
            voc_dataset.create_annotation_xml(voc_image_info, voc_annotations, xml_path)
            
            converted_images.append(filename)
            
            # Copy image if requested
            if copy_images_flag:
                dst_image_path = Path(voc_dirs['images']) / filename
                shutil.copy2(image_file, dst_image_path)
    
    # Create ImageSet files
    if converted_images:
        splits = split_dataset(converted_images, train_ratio=0.8, val_ratio=0.1, test_ratio=0.1)
        
        for split_name, image_list in splits.items():
            if image_list:
                imageset_file = os.path.join(voc_dirs['imagesets_main'], f"{split_name}.txt")
                voc_dataset.create_imageset_file(image_list, imageset_file)
        
        # Create combined trainval set
        trainval_images = splits['train'] + splits['val']
        if trainval_images:
            trainval_file = os.path.join(voc_dirs['imagesets_main'], "trainval.txt")
            voc_dataset.create_imageset_file(trainval_images, trainval_file)
    
    print(f"Conversion completed! Output saved to: {output_dir}")
    print(f"Converted {len(converted_images)} images")
    print(f"Classes: {yolo_dataset.classes}")


def convert_yolo_obb_to_voc(yolo_dir: str, images_dir: str, output_dir: str,
                           copy_images_flag: bool = True) -> None:
    """Convert YOLO OBB dataset to Pascal VOC format (converts OBB to regular bbox)"""
    yolo_dir = Path(yolo_dir)
    images_dir = Path(images_dir)
    
    # Load YOLO dataset
    yolo_dataset = YOLODataset(str(yolo_dir))
    
    # Create VOC directory structure
    voc_dirs = create_voc_directory_structure(output_dir)
    
    # Initialize VOC dataset
    voc_dataset = VOCDataset()
    
    # Get all image files
    image_files = get_image_files(images_dir)
    
    print(f"Converting {len(image_files)} images...")
    print("Note: OBB (Oriented Bounding Boxes) will be converted to axis-aligned bounding boxes")
    
    converted_images = []
    
    for image_file in tqdm(image_files):
        # Get image info
        img_width, img_height = get_image_size(image_file)
        if img_width == 0 or img_height == 0:
            continue
        
        filename = image_file.name
        
        # Read YOLO OBB annotations
        annotation_file = yolo_dir / 'labels' / f"{image_file.stem}.txt"
        annotations = read_yolo_obb_annotation(str(annotation_file))
        
        if not annotations:
            continue
        
        # Convert annotations to VOC format
        voc_annotations = []
        
        for ann in annotations:
            if ann.get('type') == 'obb':
                # Convert YOLO OBB to VOC bbox
                obb_points = ann['obb']  # [x1, y1, x2, y2, x3, y3, x4, y4] normalized
                
                # Denormalize points
                points = []
                for i in range(0, len(obb_points), 2):
                    if i + 1 < len(obb_points):
                        x = obb_points[i] * img_width
                        y = obb_points[i + 1] * img_height
                        points.extend([x, y])
                
                # Calculate axis-aligned bounding box from OBB points
                if len(points) >= 8:  # 4 points
                    x_coords = points[0::2]
                    y_coords = points[1::2]
                    
                    xmin, xmax = min(x_coords), max(x_coords)
                    ymin, ymax = min(y_coords), max(y_coords)
                    
                    # Get class name
                    class_id = ann['class_id']
                    class_name = yolo_dataset.get_class_name(class_id)
                    if class_name is None:
                        class_name = f"class_{class_id}"
                    
                    voc_annotation = {
                        'class_name': class_name,
                        'bbox': [xmin, ymin, xmax, ymax],
                        'truncated': 0,
                        'difficult': 0,
                        'type': 'detection'
                    }
                    voc_annotations.append(voc_annotation)
        
        if voc_annotations:
            # Create image info for VOC
            voc_image_info = {
                'filename': filename,
                'width': img_width,
                'height': img_height,
                'depth': 3
            }
            
            # Create XML annotation file
            xml_filename = f"{image_file.stem}.xml"
            xml_path = os.path.join(voc_dirs['annotations'], xml_filename)
            voc_dataset.create_annotation_xml(voc_image_info, voc_annotations, xml_path)
            
            converted_images.append(filename)
            
            # Copy image if requested
            if copy_images_flag:
                dst_image_path = Path(voc_dirs['images']) / filename
                shutil.copy2(image_file, dst_image_path)
    
    # Create ImageSet files
    if converted_images:
        splits = split_dataset(converted_images, train_ratio=0.8, val_ratio=0.1, test_ratio=0.1)
        
        for split_name, image_list in splits.items():
            if image_list:
                imageset_file = os.path.join(voc_dirs['imagesets_main'], f"{split_name}.txt")
                voc_dataset.create_imageset_file(image_list, imageset_file)
        
        # Create combined trainval set
        trainval_images = splits['train'] + splits['val']
        if trainval_images:
            trainval_file = os.path.join(voc_dirs['imagesets_main'], "trainval.txt")
            voc_dataset.create_imageset_file(trainval_images, trainval_file)
    
    print(f"Conversion completed! Output saved to: {output_dir}")
    print(f"Converted {len(converted_images)} images")
    print(f"Classes: {yolo_dataset.classes}")


def main():
    parser = argparse.ArgumentParser(description='Convert YOLO dataset to Pascal VOC format')
    parser.add_argument('--yolo_dir', required=True, help='Path to YOLO dataset directory')
    parser.add_argument('--images_dir', required=True, help='Path to images directory')
    parser.add_argument('--output_dir', required=True, help='Output directory for VOC dataset')
    parser.add_argument('--task', choices=['detection', 'segmentation', 'obb'], 
                       default='detection', help='Task type')
    parser.add_argument('--copy_images', action='store_true', 
                       help='Copy images to output directory')
    
    args = parser.parse_args()
    
    # Convert based on task type
    if args.task == 'detection':
        convert_yolo_to_voc_detection(args.yolo_dir, args.images_dir, args.output_dir, args.copy_images)
    elif args.task == 'segmentation':
        convert_yolo_to_voc_segmentation(args.yolo_dir, args.images_dir, args.output_dir, args.copy_images)
    elif args.task == 'obb':
        convert_yolo_obb_to_voc(args.yolo_dir, args.images_dir, args.output_dir, args.copy_images)


if __name__ == '__main__':
    main()
