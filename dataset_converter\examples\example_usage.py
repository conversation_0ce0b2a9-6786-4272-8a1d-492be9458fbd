#!/usr/bin/env python3
"""
数据集转换脚本使用示例
"""
import os
import sys
from pathlib import Path

# 添加父目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from converters.coco_to_yolo import convert_coco_to_yolo_detection, convert_coco_to_yolo_segmentation
from converters.yolo_to_coco import convert_yolo_to_coco_detection, convert_yolo_to_coco_segmentation
from converters.voc_to_coco import convert_voc_to_coco
from converters.coco_to_voc import convert_coco_to_voc
from converters.yolo_to_voc import convert_yolo_to_voc_detection
from converters.voc_to_yolo import convert_voc_to_yolo_detection
from converters.labelme_to_coco import convert_labelme_to_coco_detection
from converters.labelme_to_yolo import convert_labelme_to_yolo_detection
from converters.coco_to_labelme import convert_coco_to_labelme
from converters.yolo_to_labelme import convert_yolo_to_labelme_detection
from utils.coco_utils import COCODataset
from utils.yolo_utils import YOLODataset
from utils.voc_utils import VOCDataset
from utils.labelme_utils import LabelMeDataset


def example_coco_to_yolo():
    """示例：将COCO检测数据集转换为YOLO格式"""
    print("=== COCO转YOLO检测示例 ===")

    # 路径（请调整为您的实际数据路径）
    coco_json = "path/to/coco/annotations.json"
    output_dir = "output/yolo_detection"

    if os.path.exists(coco_json):
        # 加载COCO数据集
        coco_dataset = COCODataset(coco_json)

        # 转换为YOLO格式
        convert_coco_to_yolo_detection(coco_dataset, output_dir, copy_images=False)
        print(f"已将COCO数据集转换为YOLO格式: {output_dir}")
    else:
        print(f"未找到COCO标注文件: {coco_json}")


def example_yolo_to_coco():
    """Example: Convert YOLO detection dataset to COCO format"""
    print("=== YOLO to COCO Detection Example ===")
    
    # Paths (adjust these to your actual data)
    yolo_dir = "path/to/yolo/dataset"
    images_dir = "path/to/images"
    output_file = "output/coco_annotations.json"
    
    if os.path.exists(yolo_dir) and os.path.exists(images_dir):
        # Convert to COCO format
        convert_yolo_to_coco_detection(yolo_dir, images_dir, output_file)
        print(f"Converted YOLO dataset to COCO format: {output_file}")
    else:
        print(f"YOLO dataset or images directory not found")


def example_voc_to_yolo():
    """Example: Convert Pascal VOC dataset to YOLO format"""
    print("=== VOC to YOLO Detection Example ===")
    
    # Paths (adjust these to your actual data)
    voc_dir = "path/to/voc/dataset"
    output_dir = "output/yolo_from_voc"
    
    if os.path.exists(voc_dir):
        # Convert to YOLO format
        convert_voc_to_yolo_detection(voc_dir, output_dir, copy_images=False)
        print(f"Converted VOC dataset to YOLO format: {output_dir}")
    else:
        print(f"VOC dataset directory not found: {voc_dir}")


def example_coco_to_voc():
    """Example: Convert COCO dataset to Pascal VOC format"""
    print("=== COCO to VOC Detection Example ===")
    
    # Paths (adjust these to your actual data)
    coco_json = "path/to/coco/annotations.json"
    images_dir = "path/to/coco/images"
    output_dir = "output/voc_from_coco"
    
    if os.path.exists(coco_json):
        # Convert to VOC format
        convert_coco_to_voc(coco_json, images_dir, output_dir, copy_images_flag=False)
        print(f"Converted COCO dataset to VOC format: {output_dir}")
    else:
        print(f"COCO annotation file not found: {coco_json}")


def example_labelme_conversions():
    """示例：LabelMe格式转换"""
    print("=== LabelMe格式转换示例 ===")

    # LabelMe转COCO
    labelme_dir = "path/to/labelme/dataset"
    output_file = "output/coco_from_labelme.json"

    if os.path.exists(labelme_dir):
        convert_labelme_to_coco_detection(labelme_dir, output_file, extract_images=True)
        print(f"已将LabelMe数据集转换为COCO格式: {output_file}")

    # LabelMe转YOLO
    output_dir = "output/yolo_from_labelme"

    if os.path.exists(labelme_dir):
        convert_labelme_to_yolo_detection(labelme_dir, output_dir, extract_images=True)
        print(f"已将LabelMe数据集转换为YOLO格式: {output_dir}")

    # COCO转LabelMe
    coco_json = "path/to/coco/annotations.json"
    images_dir = "path/to/images"
    labelme_output_dir = "output/labelme_from_coco"

    if os.path.exists(coco_json):
        convert_coco_to_labelme(coco_json, images_dir, labelme_output_dir, include_image_data=False)
        print(f"已将COCO数据集转换为LabelMe格式: {labelme_output_dir}")


def example_segmentation_conversion():
    """示例：分割数据集转换"""
    print("=== 分割数据集转换示例 ===")

    # COCO转YOLO分割
    coco_json = "path/to/coco/segmentation_annotations.json"
    output_dir = "output/yolo_segmentation"

    if os.path.exists(coco_json):
        coco_dataset = COCODataset(coco_json)
        convert_coco_to_yolo_segmentation(coco_dataset, output_dir, copy_images=False)
        print(f"已将COCO分割转换为YOLO格式: {output_dir}")

    # YOLO转COCO分割
    yolo_dir = "path/to/yolo/segmentation"
    images_dir = "path/to/images"
    output_file = "output/coco_segmentation.json"

    if os.path.exists(yolo_dir) and os.path.exists(images_dir):
        convert_yolo_to_coco_segmentation(yolo_dir, images_dir, output_file)
        print(f"已将YOLO分割转换为COCO格式: {output_file}")


def example_create_sample_data():
    """Create sample data for testing"""
    print("=== Creating Sample Data ===")
    
    # Create sample COCO dataset
    sample_dir = Path("sample_data")
    sample_dir.mkdir(exist_ok=True)
    
    # Create sample COCO annotation
    coco_dataset = COCODataset()
    
    # Add categories
    coco_dataset.add_category(1, "person")
    coco_dataset.add_category(2, "car")
    
    # Add sample image
    coco_dataset.add_image(1, "sample_image.jpg", 640, 480)
    
    # Add sample annotations
    coco_dataset.add_detection_annotation(1, 1, 1, [100, 100, 200, 150])  # person
    coco_dataset.add_detection_annotation(2, 1, 2, [300, 200, 150, 100])  # car
    
    # Save sample COCO dataset
    sample_coco_file = sample_dir / "sample_coco.json"
    coco_dataset.save_annotations(str(sample_coco_file))
    print(f"Created sample COCO dataset: {sample_coco_file}")
    
    # Create sample YOLO dataset
    yolo_dir = sample_dir / "sample_yolo"
    yolo_dir.mkdir(exist_ok=True)
    (yolo_dir / "labels").mkdir(exist_ok=True)
    
    # Create classes file
    with open(yolo_dir / "classes.txt", "w") as f:
        f.write("person\ncar\n")
    
    # Create sample annotation file
    with open(yolo_dir / "labels" / "sample_image.txt", "w") as f:
        # person: class_id=0, center=(200, 175), size=(200, 150), normalized
        f.write("0 0.3125 0.3646 0.3125 0.3125\n")
        # car: class_id=1, center=(375, 250), size=(150, 100), normalized  
        f.write("1 0.5859 0.5208 0.2344 0.2083\n")
    
    print(f"Created sample YOLO dataset: {yolo_dir}")


def main():
    """Run all examples"""
    print("Dataset Converter Examples")
    print("=" * 50)
    
    # Create sample data first
    example_create_sample_data()
    
    print("\nNote: The following examples require actual dataset paths.")
    print("Please modify the paths in the example functions to point to your actual data.\n")
    
    # Run examples (these will show messages about missing files)
    example_coco_to_yolo()
    print()
    
    example_yolo_to_coco()
    print()
    
    example_voc_to_yolo()
    print()
    
    example_coco_to_voc()
    print()
    
    example_segmentation_conversion()
    print()
    
    print("Examples completed!")
    print("\nTo use the converters with your own data:")
    print("1. Install dependencies: pip install -r requirements.txt")
    print("2. Use the command-line scripts in the converters/ directory")
    print("3. Or import and use the functions in your own Python scripts")


if __name__ == "__main__":
    main()
