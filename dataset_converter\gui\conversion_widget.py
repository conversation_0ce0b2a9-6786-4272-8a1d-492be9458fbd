"""
Conversion configuration widget for dataset converter G<PERSON>
"""
from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QComboBox, QLabel, 
    QPushButton, QGroupBox, QTextEdit, QSplitter
)
from PySide6.QtCore import Signal, Qt
from .file_selector import ParametersGroup
from .progress_widget import ProgressWidget
from .utils import get_conversion_types, ConversionWorker, show_error_message, show_info_message


class ConversionWidget(QWidget):
    """转换配置组件"""
    
    conversion_started = Signal()
    conversion_finished = Signal(bool, str)
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.conversion_types = get_conversion_types()
        self.current_conversion = None
        self.conversion_worker = None
        
        self.setup_ui()
        self.connect_signals()
    
    def setup_ui(self):
        """设置UI"""
        layout = QVBoxLayout(self)
        
        # 转换类型选择
        type_group = QGroupBox("选择转换类型")
        type_layout = QVBoxLayout(type_group)
        
        type_select_layout = QHBoxLayout()
        type_select_layout.addWidget(QLabel("转换类型:"))
        
        self.conversion_combo = QComboBox()
        self.conversion_combo.addItems(list(self.conversion_types.keys()))
        self.conversion_combo.currentTextChanged.connect(self.on_conversion_type_changed)
        type_select_layout.addWidget(self.conversion_combo)
        
        type_select_layout.addStretch()
        type_layout.addLayout(type_select_layout)
        
        # 描述标签
        self.description_label = QLabel()
        self.description_label.setWordWrap(True)
        self.description_label.setStyleSheet("color: #666; font-style: italic; margin: 5px 0;")
        type_layout.addWidget(self.description_label)
        
        layout.addWidget(type_group)
        
        # 创建分割器
        splitter = QSplitter(Qt.Horizontal)
        
        # 左侧：参数配置
        left_widget = QWidget()
        left_layout = QVBoxLayout(left_widget)
        left_layout.setContentsMargins(0, 0, 0, 0)
        
        # 参数配置组
        self.params_group = ParametersGroup("转换参数", {})
        left_layout.addWidget(self.params_group)
        
        # 控制按钮
        buttons_layout = QHBoxLayout()
        
        self.start_button = QPushButton("开始转换")
        self.start_button.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                padding: 8px 16px;
                font-weight: bold;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
            QPushButton:disabled {
                background-color: #cccccc;
            }
        """)
        self.start_button.clicked.connect(self.start_conversion)
        buttons_layout.addWidget(self.start_button)
        
        self.reset_button = QPushButton("重置")
        self.reset_button.clicked.connect(self.reset_parameters)
        buttons_layout.addWidget(self.reset_button)
        
        buttons_layout.addStretch()
        left_layout.addLayout(buttons_layout)
        
        splitter.addWidget(left_widget)
        
        # 右侧：进度和日志
        self.progress_widget = ProgressWidget()
        splitter.addWidget(self.progress_widget)
        
        # 设置分割器比例
        splitter.setSizes([400, 400])
        
        layout.addWidget(splitter)
        
        # 初始化第一个转换类型
        if self.conversion_types:
            first_type = list(self.conversion_types.keys())[0]
            self.on_conversion_type_changed(first_type)
    
    def connect_signals(self):
        """连接信号"""
        self.progress_widget.cancel_requested.connect(self.cancel_conversion)
    
    def on_conversion_type_changed(self, conversion_type: str):
        """转换类型改变时的处理"""
        if conversion_type not in self.conversion_types:
            return
        
        self.current_conversion = conversion_type
        config = self.conversion_types[conversion_type]
        
        # 更新描述
        description = config.get("description", "")
        self.description_label.setText(description)
        
        # 更新参数组
        params_config = config.get("params", {})
        
        # 移除旧的参数组
        self.params_group.setParent(None)
        
        # 创建新的参数组
        self.params_group = ParametersGroup("转换参数", params_config)
        
        # 添加到布局中
        layout = self.layout()
        splitter = layout.itemAt(1).widget()  # 获取分割器
        left_widget = splitter.widget(0)
        left_layout = left_widget.layout()
        left_layout.insertWidget(0, self.params_group)
        
        # 重置进度
        self.progress_widget.reset()
    
    def start_conversion(self):
        """开始转换"""
        if not self.current_conversion:
            show_error_message(self, "错误", "请选择转换类型")
            return
        
        # 验证参数
        is_valid, error_msg = self.params_group.validate_parameters()
        if not is_valid:
            show_error_message(self, "参数错误", error_msg)
            return
        
        # 获取参数
        params = self.params_group.get_parameters()
        
        # 创建转换工作线程
        self.conversion_worker = ConversionWorker(self.current_conversion, params)
        self.conversion_worker.progress_updated.connect(self.progress_widget.update_progress)
        self.conversion_worker.log_updated.connect(self.progress_widget.add_log)
        self.conversion_worker.finished.connect(self.on_conversion_finished)
        
        # 更新UI状态
        self.start_button.setEnabled(False)
        self.conversion_combo.setEnabled(False)
        
        # 开始转换
        self.progress_widget.start_conversion()
        self.conversion_worker.start()
        
        self.conversion_started.emit()
    
    def cancel_conversion(self):
        """取消转换"""
        if self.conversion_worker and self.conversion_worker.isRunning():
            self.conversion_worker.terminate()
            self.conversion_worker.wait()
            
            self.progress_widget.finish_conversion(False, "转换已取消")
            self.reset_ui_state()
    
    def on_conversion_finished(self, success: bool, message: str):
        """转换完成处理"""
        self.progress_widget.finish_conversion(success, message)
        self.reset_ui_state()
        
        if success:
            show_info_message(self, "转换完成", message)
        else:
            show_error_message(self, "转换失败", message)
        
        self.conversion_finished.emit(success, message)
    
    def reset_ui_state(self):
        """重置UI状态"""
        self.start_button.setEnabled(True)
        self.conversion_combo.setEnabled(True)
    
    def reset_parameters(self):
        """重置参数"""
        if self.current_conversion:
            config = self.conversion_types[self.current_conversion]
            params_config = config.get("params", {})
            
            # 重置为默认值
            default_params = {}
            for param_name, param_config in params_config.items():
                default_value = param_config.get("default")
                if default_value is not None:
                    default_params[param_name] = default_value
            
            self.params_group.set_parameters(default_params)
        
        # 重置进度
        self.progress_widget.reset()
    
    def get_current_conversion_type(self) -> str:
        """获取当前选择的转换类型"""
        return self.current_conversion
    
    def get_current_parameters(self) -> dict:
        """获取当前参数"""
        return self.params_group.get_parameters()
    
    def set_conversion_type(self, conversion_type: str):
        """设置转换类型"""
        if conversion_type in self.conversion_types:
            self.conversion_combo.setCurrentText(conversion_type)
    
    def set_parameters(self, params: dict):
        """设置参数"""
        self.params_group.set_parameters(params)
