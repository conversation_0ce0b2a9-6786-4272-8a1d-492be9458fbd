"""
File selector widget for dataset converter G<PERSON>
"""
import os
from pathlib import Path
from typing import Optional
from PySide6.QtWidgets import (
    QWidget, QHBoxLayout, QVBoxLayout, QLineEdit, QPushButton, 
    QLabel, QGroupBox, QCheckBox, QComboBox, QSpinBox, QDoubleSpinBox
)
from PySide6.QtCore import Signal
from .utils import select_file, select_directory, select_save_file


class FileSelector(QWidget):
    """文件选择器组件"""
    
    file_selected = Signal(str)
    
    def __init__(self, label: str, file_type: str = "file", file_filter: str = "All Files (*)", parent=None):
        super().__init__(parent)
        self.file_type = file_type  # "file", "directory", "save_file"
        self.file_filter = file_filter
        
        self.setup_ui(label)
    
    def setup_ui(self, label: str):
        """设置UI"""
        layout = QHBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        
        # 标签
        self.label = QLabel(label)
        self.label.setMinimumWidth(120)
        layout.addWidget(self.label)
        
        # 路径输入框
        self.path_edit = QLineEdit()
        self.path_edit.setPlaceholderText(f"选择{label}")
        layout.addWidget(self.path_edit)
        
        # 浏览按钮
        self.browse_button = QPushButton("浏览...")
        self.browse_button.clicked.connect(self.browse_file)
        layout.addWidget(self.browse_button)
    
    def browse_file(self):
        """浏览文件"""
        if self.file_type == "file":
            file_path = select_file(self, f"选择{self.label.text()}", self.file_filter)
        elif self.file_type == "directory":
            file_path = select_directory(self, f"选择{self.label.text()}")
        elif self.file_type == "save_file":
            file_path = select_save_file(self, f"保存{self.label.text()}", self.file_filter)
        else:
            file_path = ""
        
        if file_path:
            self.path_edit.setText(file_path)
            self.file_selected.emit(file_path)
    
    def get_path(self) -> str:
        """获取选择的路径"""
        return self.path_edit.text().strip()
    
    def set_path(self, path: str):
        """设置路径"""
        self.path_edit.setText(path)
    
    def is_valid(self) -> bool:
        """检查路径是否有效"""
        path = self.get_path()
        if not path:
            return False
        
        if self.file_type == "file" or self.file_type == "save_file":
            return os.path.isfile(path) or self.file_type == "save_file"
        elif self.file_type == "directory":
            return os.path.isdir(path)
        
        return False


class ParameterWidget(QWidget):
    """参数配置组件"""
    
    def __init__(self, param_name: str, param_config: dict, parent=None):
        super().__init__(parent)
        self.param_name = param_name
        self.param_config = param_config
        self.widget = None
        
        self.setup_ui()
    
    def setup_ui(self):
        """设置UI"""
        layout = QHBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        
        # 参数标签
        label = QLabel(self.param_config.get("description", self.param_name))
        label.setMinimumWidth(120)
        layout.addWidget(label)
        
        param_type = self.param_config.get("type", "text")
        
        if param_type == "file":
            file_filter = self.param_config.get("filter", "All Files (*)")
            self.widget = FileSelector("", "file", file_filter)
            layout.addWidget(self.widget)
            
        elif param_type == "directory":
            self.widget = FileSelector("", "directory")
            layout.addWidget(self.widget)
            
        elif param_type == "save_file":
            file_filter = self.param_config.get("filter", "All Files (*)")
            self.widget = FileSelector("", "save_file", file_filter)
            layout.addWidget(self.widget)
            
        elif param_type == "bool":
            self.widget = QCheckBox()
            self.widget.setChecked(self.param_config.get("default", False))
            layout.addWidget(self.widget)
            
        elif param_type == "choice":
            self.widget = QComboBox()
            choices = self.param_config.get("choices", [])
            self.widget.addItems(choices)
            default = self.param_config.get("default")
            if default and default in choices:
                self.widget.setCurrentText(default)
            layout.addWidget(self.widget)
            
        elif param_type == "int":
            self.widget = QSpinBox()
            self.widget.setRange(-999999, 999999)
            self.widget.setValue(self.param_config.get("default", 0))
            layout.addWidget(self.widget)
            
        elif param_type == "float":
            self.widget = QDoubleSpinBox()
            self.widget.setRange(-999999.0, 999999.0)
            self.widget.setDecimals(3)
            self.widget.setValue(self.param_config.get("default", 0.0))
            layout.addWidget(self.widget)
            
        else:  # text
            self.widget = QLineEdit()
            self.widget.setText(str(self.param_config.get("default", "")))
            layout.addWidget(self.widget)
    
    def get_value(self):
        """获取参数值"""
        param_type = self.param_config.get("type", "text")
        
        if param_type in ["file", "directory", "save_file"]:
            return self.widget.get_path()
        elif param_type == "bool":
            return self.widget.isChecked()
        elif param_type == "choice":
            return self.widget.currentText()
        elif param_type == "int":
            return self.widget.value()
        elif param_type == "float":
            return self.widget.value()
        else:  # text
            return self.widget.text()
    
    def set_value(self, value):
        """设置参数值"""
        param_type = self.param_config.get("type", "text")
        
        if param_type in ["file", "directory", "save_file"]:
            self.widget.set_path(str(value))
        elif param_type == "bool":
            self.widget.setChecked(bool(value))
        elif param_type == "choice":
            self.widget.setCurrentText(str(value))
        elif param_type == "int":
            self.widget.setValue(int(value))
        elif param_type == "float":
            self.widget.setValue(float(value))
        else:  # text
            self.widget.setText(str(value))
    
    def is_valid(self) -> bool:
        """检查参数是否有效"""
        if not self.param_config.get("required", False):
            return True
        
        param_type = self.param_config.get("type", "text")
        value = self.get_value()
        
        if param_type in ["file", "directory", "save_file"]:
            return bool(value) and (param_type == "save_file" or os.path.exists(value))
        elif param_type == "text":
            return bool(value.strip())
        else:
            return value is not None


class ParametersGroup(QGroupBox):
    """参数组组件"""
    
    def __init__(self, title: str, params_config: dict, parent=None):
        super().__init__(title, parent)
        self.params_config = params_config
        self.param_widgets = {}
        
        self.setup_ui()
    
    def setup_ui(self):
        """设置UI"""
        layout = QVBoxLayout(self)
        
        for param_name, param_config in self.params_config.items():
            param_widget = ParameterWidget(param_name, param_config)
            self.param_widgets[param_name] = param_widget
            layout.addWidget(param_widget)
    
    def get_parameters(self) -> dict:
        """获取所有参数值"""
        params = {}
        for param_name, widget in self.param_widgets.items():
            value = widget.get_value()
            if value is not None and value != "":
                params[param_name] = value
        return params
    
    def set_parameters(self, params: dict):
        """设置参数值"""
        for param_name, value in params.items():
            if param_name in self.param_widgets:
                self.param_widgets[param_name].set_value(value)
    
    def validate_parameters(self) -> tuple[bool, str]:
        """验证参数"""
        for param_name, widget in self.param_widgets.items():
            if not widget.is_valid():
                param_desc = widget.param_config.get("description", param_name)
                return False, f"参数 '{param_desc}' 无效或缺失"
        return True, ""
