"""
Main window for dataset converter GUI
"""
import sys
import os
from pathlib import Path
from PySide6.QtWidgets import (
    QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QMenuBar, 
    QMenu, QStatusBar, QSplitter, QTabWidget, QMessageBox,
    QAction, QToolBar, QLabel
)
from PySide6.QtCore import Qt, QSettings, QTimer
from PySide6.QtGui import QIcon, QKeySequence

from .conversion_widget import ConversionWidget
from .preview_widget import PreviewWidget
from .utils import show_info_message, show_error_message


class MainWindow(QMainWindow):
    """主窗口"""
    
    def __init__(self):
        super().__init__()
        self.settings = QSettings("DatasetConverter", "GUI")
        
        self.setup_ui()
        self.setup_menu()
        self.setup_toolbar()
        self.setup_statusbar()
        self.connect_signals()
        self.restore_settings()
        
        # 设置窗口属性
        self.setWindowTitle("数据集转换器 - Dataset Converter")
        self.setMinimumSize(1000, 700)
        self.resize(1200, 800)
    
    def setup_ui(self):
        """设置UI"""
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 主布局
        main_layout = QHBoxLayout(central_widget)
        
        # 创建分割器
        splitter = QSplitter(Qt.Horizontal)
        
        # 左侧：转换配置
        self.conversion_widget = ConversionWidget()
        splitter.addWidget(self.conversion_widget)
        
        # 右侧：预览
        self.preview_widget = PreviewWidget()
        splitter.addWidget(self.preview_widget)
        
        # 设置分割器比例 (转换:预览 = 2:1)
        splitter.setSizes([800, 400])
        
        main_layout.addWidget(splitter)
    
    def setup_menu(self):
        """设置菜单栏"""
        menubar = self.menuBar()
        
        # 文件菜单
        file_menu = menubar.addMenu("文件(&F)")
        
        # 新建配置
        new_action = QAction("新建配置(&N)", self)
        new_action.setShortcut(QKeySequence.New)
        new_action.setStatusTip("创建新的转换配置")
        new_action.triggered.connect(self.new_configuration)
        file_menu.addAction(new_action)
        
        # 打开配置
        open_action = QAction("打开配置(&O)", self)
        open_action.setShortcut(QKeySequence.Open)
        open_action.setStatusTip("打开保存的转换配置")
        open_action.triggered.connect(self.open_configuration)
        file_menu.addAction(open_action)
        
        # 保存配置
        save_action = QAction("保存配置(&S)", self)
        save_action.setShortcut(QKeySequence.Save)
        save_action.setStatusTip("保存当前转换配置")
        save_action.triggered.connect(self.save_configuration)
        file_menu.addAction(save_action)
        
        file_menu.addSeparator()
        
        # 退出
        exit_action = QAction("退出(&X)", self)
        exit_action.setShortcut(QKeySequence.Quit)
        exit_action.setStatusTip("退出应用程序")
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)
        
        # 工具菜单
        tools_menu = menubar.addMenu("工具(&T)")
        
        # 批量转换
        batch_action = QAction("批量转换(&B)", self)
        batch_action.setStatusTip("批量转换多个数据集")
        batch_action.triggered.connect(self.batch_conversion)
        tools_menu.addAction(batch_action)
        
        # 数据集验证
        validate_action = QAction("数据集验证(&V)", self)
        validate_action.setStatusTip("验证数据集格式和完整性")
        validate_action.triggered.connect(self.validate_dataset)
        tools_menu.addAction(validate_action)
        
        tools_menu.addSeparator()
        
        # 设置
        settings_action = QAction("设置(&S)", self)
        settings_action.setStatusTip("应用程序设置")
        settings_action.triggered.connect(self.show_settings)
        tools_menu.addAction(settings_action)
        
        # 帮助菜单
        help_menu = menubar.addMenu("帮助(&H)")
        
        # 使用说明
        help_action = QAction("使用说明(&H)", self)
        help_action.setShortcut(QKeySequence.HelpContents)
        help_action.setStatusTip("查看使用说明")
        help_action.triggered.connect(self.show_help)
        help_menu.addAction(help_action)
        
        # 关于
        about_action = QAction("关于(&A)", self)
        about_action.setStatusTip("关于数据集转换器")
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)
    
    def setup_toolbar(self):
        """设置工具栏"""
        toolbar = QToolBar("主工具栏")
        self.addToolBar(toolbar)
        
        # 新建
        new_action = QAction("新建", self)
        new_action.setStatusTip("创建新的转换配置")
        new_action.triggered.connect(self.new_configuration)
        toolbar.addAction(new_action)
        
        # 打开
        open_action = QAction("打开", self)
        open_action.setStatusTip("打开保存的转换配置")
        open_action.triggered.connect(self.open_configuration)
        toolbar.addAction(open_action)
        
        # 保存
        save_action = QAction("保存", self)
        save_action.setStatusTip("保存当前转换配置")
        save_action.triggered.connect(self.save_configuration)
        toolbar.addAction(save_action)
        
        toolbar.addSeparator()
        
        # 开始转换
        convert_action = QAction("开始转换", self)
        convert_action.setStatusTip("开始数据集转换")
        convert_action.triggered.connect(self.start_conversion)
        toolbar.addAction(convert_action)
        
        toolbar.addSeparator()
        
        # 刷新预览
        refresh_action = QAction("刷新预览", self)
        refresh_action.setStatusTip("刷新数据集预览")
        refresh_action.triggered.connect(self.refresh_preview)
        toolbar.addAction(refresh_action)
    
    def setup_statusbar(self):
        """设置状态栏"""
        self.statusbar = QStatusBar()
        self.setStatusBar(self.statusbar)
        
        # 状态标签
        self.status_label = QLabel("准备就绪")
        self.statusbar.addWidget(self.status_label)
        
        # 进度信息
        self.progress_label = QLabel("")
        self.statusbar.addPermanentWidget(self.progress_label)
    
    def connect_signals(self):
        """连接信号"""
        # 转换相关信号
        self.conversion_widget.conversion_started.connect(self.on_conversion_started)
        self.conversion_widget.conversion_finished.connect(self.on_conversion_finished)
    
    def restore_settings(self):
        """恢复设置"""
        # 恢复窗口几何
        geometry = self.settings.value("geometry")
        if geometry:
            self.restoreGeometry(geometry)
        
        # 恢复窗口状态
        state = self.settings.value("windowState")
        if state:
            self.restoreState(state)
    
    def save_settings(self):
        """保存设置"""
        self.settings.setValue("geometry", self.saveGeometry())
        self.settings.setValue("windowState", self.saveState())
    
    def closeEvent(self, event):
        """关闭事件"""
        self.save_settings()
        event.accept()
    
    # 菜单和工具栏动作
    def new_configuration(self):
        """新建配置"""
        self.conversion_widget.reset_parameters()
        self.preview_widget.clear_preview()
        self.status_label.setText("已创建新配置")
    
    def open_configuration(self):
        """打开配置"""
        from .utils import select_file
        file_path = select_file(self, "打开配置文件", "JSON Files (*.json)")
        if file_path:
            try:
                import json
                with open(file_path, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                
                # 设置转换类型和参数
                conversion_type = config.get("conversion_type", "")
                parameters = config.get("parameters", {})
                
                if conversion_type:
                    self.conversion_widget.set_conversion_type(conversion_type)
                    self.conversion_widget.set_parameters(parameters)
                
                self.status_label.setText(f"已加载配置: {os.path.basename(file_path)}")
                
            except Exception as e:
                show_error_message(self, "错误", f"加载配置失败: {str(e)}")
    
    def save_configuration(self):
        """保存配置"""
        from .utils import select_save_file
        file_path = select_save_file(self, "保存配置文件", "JSON Files (*.json)")
        if file_path:
            try:
                config = {
                    "conversion_type": self.conversion_widget.get_current_conversion_type(),
                    "parameters": self.conversion_widget.get_current_parameters()
                }
                
                import json
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(config, f, ensure_ascii=False, indent=2)
                
                self.status_label.setText(f"已保存配置: {os.path.basename(file_path)}")
                
            except Exception as e:
                show_error_message(self, "错误", f"保存配置失败: {str(e)}")
    
    def start_conversion(self):
        """开始转换"""
        self.conversion_widget.start_conversion()
    
    def refresh_preview(self):
        """刷新预览"""
        self.preview_widget.refresh_preview()
        self.status_label.setText("已刷新预览")
    
    def batch_conversion(self):
        """批量转换"""
        show_info_message(self, "功能开发中", "批量转换功能正在开发中，敬请期待！")
    
    def validate_dataset(self):
        """验证数据集"""
        show_info_message(self, "功能开发中", "数据集验证功能正在开发中，敬请期待！")
    
    def show_settings(self):
        """显示设置"""
        show_info_message(self, "功能开发中", "设置功能正在开发中，敬请期待！")
    
    def show_help(self):
        """显示帮助"""
        help_text = """
数据集转换器使用说明

1. 选择转换类型
   - 在左侧选择需要的转换类型（如COCO→YOLO）

2. 配置参数
   - 根据转换类型填写相应的参数
   - 必填参数会有相应提示

3. 开始转换
   - 点击"开始转换"按钮开始转换
   - 可以在进度区域查看转换状态和日志

4. 预览数据集
   - 右侧预览区域可以查看数据集的基本信息
   - 包括类别、样本等信息

5. 保存配置
   - 可以保存当前的转换配置以便重复使用

支持的格式：
- COCO (JSON)
- YOLO (TXT)
- Pascal VOC (XML)
- LabelMe (JSON)
- ImageNet (目录结构)

支持的任务：
- 目标检测
- 实例分割
- 关键点检测
- 图像分类
        """
        
        msg_box = QMessageBox(self)
        msg_box.setWindowTitle("使用说明")
        msg_box.setText(help_text)
        msg_box.setIcon(QMessageBox.Information)
        msg_box.exec()
    
    def show_about(self):
        """显示关于"""
        about_text = """
数据集转换器 v1.0

一个全面的计算机视觉数据集格式转换工具包。

支持多种数据集格式之间的相互转换，包括：
• COCO
• YOLO  
• Pascal VOC
• LabelMe
• ImageNet

支持的任务类型：
• 目标检测
• 实例分割
• 关键点检测
• 图像分类

开发者：Dataset Converter Team
        """
        
        msg_box = QMessageBox(self)
        msg_box.setWindowTitle("关于数据集转换器")
        msg_box.setText(about_text)
        msg_box.setIcon(QMessageBox.Information)
        msg_box.exec()
    
    # 转换事件处理
    def on_conversion_started(self):
        """转换开始时的处理"""
        self.status_label.setText("转换进行中...")
        self.progress_label.setText("正在转换数据集")
    
    def on_conversion_finished(self, success: bool, message: str):
        """转换完成时的处理"""
        if success:
            self.status_label.setText("转换完成")
            self.progress_label.setText("转换成功")
        else:
            self.status_label.setText("转换失败")
            self.progress_label.setText("转换失败")
        
        # 3秒后清除进度信息
        QTimer.singleShot(3000, lambda: self.progress_label.setText(""))
