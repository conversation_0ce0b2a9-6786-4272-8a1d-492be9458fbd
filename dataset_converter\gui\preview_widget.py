"""
Preview widget for dataset converter GUI
"""
import os
import json
from pathlib import Path
from typing import Dict, List, Optional
from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QTextEdit, QLabel, 
    QPushButton, QGroupBox, QTabWidget, QTableWidget, 
    QTableWidgetItem, QHeaderView, QScrollArea
)
from PySide6.QtCore import Qt, QThread, Signal
from PySide6.QtGui import QFont, QPixmap


class DatasetInfoWorker(QThread):
    """数据集信息加载工作线程"""
    
    info_loaded = Signal(dict)
    error_occurred = Signal(str)
    
    def __init__(self, file_path: str, dataset_type: str):
        super().__init__()
        self.file_path = file_path
        self.dataset_type = dataset_type
    
    def run(self):
        """加载数据集信息"""
        try:
            if self.dataset_type == "coco":
                info = self.load_coco_info()
            elif self.dataset_type == "yolo":
                info = self.load_yolo_info()
            elif self.dataset_type == "voc":
                info = self.load_voc_info()
            elif self.dataset_type == "labelme":
                info = self.load_labelme_info()
            else:
                info = {"error": "不支持的数据集类型"}
            
            self.info_loaded.emit(info)
            
        except Exception as e:
            self.error_occurred.emit(f"加载数据集信息失败: {str(e)}")
    
    def load_coco_info(self) -> Dict:
        """加载COCO数据集信息"""
        with open(self.file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        info = {
            "type": "COCO",
            "images_count": len(data.get("images", [])),
            "annotations_count": len(data.get("annotations", [])),
            "categories_count": len(data.get("categories", [])),
            "categories": [cat.get("name", "Unknown") for cat in data.get("categories", [])],
            "info": data.get("info", {}),
            "sample_images": data.get("images", [])[:5],  # 前5张图片
            "sample_annotations": data.get("annotations", [])[:10]  # 前10个标注
        }
        
        return info
    
    def load_yolo_info(self) -> Dict:
        """加载YOLO数据集信息"""
        dataset_path = Path(self.file_path)
        
        # 查找classes.txt或data.yaml
        classes_file = dataset_path / "classes.txt"
        yaml_file = dataset_path / "data.yaml"
        
        classes = []
        if classes_file.exists():
            with open(classes_file, 'r', encoding='utf-8') as f:
                classes = [line.strip() for line in f.readlines() if line.strip()]
        elif yaml_file.exists():
            import yaml
            with open(yaml_file, 'r', encoding='utf-8') as f:
                yaml_data = yaml.safe_load(f)
                classes = yaml_data.get("names", [])
        
        # 统计图片和标注文件
        images_dir = dataset_path / "images"
        labels_dir = dataset_path / "labels"
        
        image_files = []
        label_files = []
        
        if images_dir.exists():
            image_files = list(images_dir.glob("*.jpg")) + list(images_dir.glob("*.png"))
        
        if labels_dir.exists():
            label_files = list(labels_dir.glob("*.txt"))
        
        info = {
            "type": "YOLO",
            "images_count": len(image_files),
            "labels_count": len(label_files),
            "categories_count": len(classes),
            "categories": classes,
            "sample_images": [str(f.name) for f in image_files[:5]],
            "sample_labels": [str(f.name) for f in label_files[:5]]
        }
        
        return info
    
    def load_voc_info(self) -> Dict:
        """加载VOC数据集信息"""
        dataset_path = Path(self.file_path)
        
        # 查找Annotations目录
        annotations_dir = dataset_path / "Annotations"
        images_dir = dataset_path / "JPEGImages"
        
        xml_files = []
        image_files = []
        
        if annotations_dir.exists():
            xml_files = list(annotations_dir.glob("*.xml"))
        
        if images_dir.exists():
            image_files = list(images_dir.glob("*.jpg")) + list(images_dir.glob("*.png"))
        
        # 从XML文件中提取类别信息
        categories = set()
        if xml_files:
            import xml.etree.ElementTree as ET
            for xml_file in xml_files[:10]:  # 只检查前10个文件
                try:
                    tree = ET.parse(xml_file)
                    root = tree.getroot()
                    for obj in root.findall("object"):
                        name = obj.find("name")
                        if name is not None:
                            categories.add(name.text)
                except:
                    continue
        
        info = {
            "type": "Pascal VOC",
            "images_count": len(image_files),
            "annotations_count": len(xml_files),
            "categories_count": len(categories),
            "categories": list(categories),
            "sample_images": [str(f.name) for f in image_files[:5]],
            "sample_annotations": [str(f.name) for f in xml_files[:5]]
        }
        
        return info
    
    def load_labelme_info(self) -> Dict:
        """加载LabelMe数据集信息"""
        dataset_path = Path(self.file_path)
        
        json_files = list(dataset_path.glob("*.json"))
        
        categories = set()
        total_shapes = 0
        
        for json_file in json_files[:20]:  # 只检查前20个文件
            try:
                with open(json_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    shapes = data.get("shapes", [])
                    total_shapes += len(shapes)
                    for shape in shapes:
                        label = shape.get("label", "")
                        if label:
                            categories.add(label)
            except:
                continue
        
        info = {
            "type": "LabelMe",
            "files_count": len(json_files),
            "total_shapes": total_shapes,
            "categories_count": len(categories),
            "categories": list(categories),
            "sample_files": [str(f.name) for f in json_files[:5]]
        }
        
        return info


class PreviewWidget(QWidget):
    """预览组件"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.info_worker = None
        self.setup_ui()
    
    def setup_ui(self):
        """设置UI"""
        layout = QVBoxLayout(self)
        
        # 标题
        title_label = QLabel("数据集预览")
        title_label.setStyleSheet("font-size: 16px; font-weight: bold; margin-bottom: 10px;")
        layout.addWidget(title_label)
        
        # 创建选项卡
        self.tab_widget = QTabWidget()
        
        # 基本信息选项卡
        self.info_tab = self.create_info_tab()
        self.tab_widget.addTab(self.info_tab, "基本信息")
        
        # 类别信息选项卡
        self.categories_tab = self.create_categories_tab()
        self.tab_widget.addTab(self.categories_tab, "类别信息")
        
        # 样本预览选项卡
        self.samples_tab = self.create_samples_tab()
        self.tab_widget.addTab(self.samples_tab, "样本预览")
        
        layout.addWidget(self.tab_widget)
        
        # 刷新按钮
        refresh_layout = QHBoxLayout()
        self.refresh_button = QPushButton("刷新预览")
        self.refresh_button.clicked.connect(self.refresh_preview)
        refresh_layout.addWidget(self.refresh_button)
        refresh_layout.addStretch()
        
        layout.addLayout(refresh_layout)
    
    def create_info_tab(self) -> QWidget:
        """创建基本信息选项卡"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        self.info_text = QTextEdit()
        self.info_text.setReadOnly(True)
        self.info_text.setMaximumHeight(300)
        
        # 设置等宽字体
        font = QFont("Consolas", 10)
        if not font.exactMatch():
            font = QFont("Courier New", 10)
        self.info_text.setFont(font)
        
        layout.addWidget(self.info_text)
        
        return widget
    
    def create_categories_tab(self) -> QWidget:
        """创建类别信息选项卡"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        self.categories_table = QTableWidget()
        self.categories_table.setColumnCount(2)
        self.categories_table.setHorizontalHeaderLabels(["ID", "类别名称"])
        
        # 设置表格属性
        header = self.categories_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(1, QHeaderView.Stretch)
        
        layout.addWidget(self.categories_table)
        
        return widget
    
    def create_samples_tab(self) -> QWidget:
        """创建样本预览选项卡"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 创建滚动区域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        
        self.samples_widget = QWidget()
        self.samples_layout = QVBoxLayout(self.samples_widget)
        
        scroll_area.setWidget(self.samples_widget)
        layout.addWidget(scroll_area)
        
        return widget
    
    def load_dataset_preview(self, file_path: str, dataset_type: str):
        """加载数据集预览"""
        if not file_path or not os.path.exists(file_path):
            self.clear_preview()
            return
        
        # 显示加载状态
        self.info_text.setText("正在加载数据集信息...")
        
        # 创建工作线程
        self.info_worker = DatasetInfoWorker(file_path, dataset_type)
        self.info_worker.info_loaded.connect(self.display_dataset_info)
        self.info_worker.error_occurred.connect(self.display_error)
        self.info_worker.start()
    
    def display_dataset_info(self, info: Dict):
        """显示数据集信息"""
        # 显示基本信息
        info_text = f"数据集类型: {info.get('type', 'Unknown')}\n\n"
        
        if info.get("type") == "COCO":
            info_text += f"图片数量: {info.get('images_count', 0)}\n"
            info_text += f"标注数量: {info.get('annotations_count', 0)}\n"
            info_text += f"类别数量: {info.get('categories_count', 0)}\n\n"
            
            dataset_info = info.get('info', {})
            if dataset_info:
                info_text += "数据集信息:\n"
                for key, value in dataset_info.items():
                    info_text += f"  {key}: {value}\n"
        
        elif info.get("type") == "YOLO":
            info_text += f"图片数量: {info.get('images_count', 0)}\n"
            info_text += f"标注数量: {info.get('labels_count', 0)}\n"
            info_text += f"类别数量: {info.get('categories_count', 0)}\n"
        
        elif info.get("type") == "Pascal VOC":
            info_text += f"图片数量: {info.get('images_count', 0)}\n"
            info_text += f"标注数量: {info.get('annotations_count', 0)}\n"
            info_text += f"类别数量: {info.get('categories_count', 0)}\n"
        
        elif info.get("type") == "LabelMe":
            info_text += f"文件数量: {info.get('files_count', 0)}\n"
            info_text += f"标注形状数量: {info.get('total_shapes', 0)}\n"
            info_text += f"类别数量: {info.get('categories_count', 0)}\n"
        
        self.info_text.setText(info_text)
        
        # 显示类别信息
        categories = info.get('categories', [])
        self.categories_table.setRowCount(len(categories))
        
        for i, category in enumerate(categories):
            self.categories_table.setItem(i, 0, QTableWidgetItem(str(i)))
            self.categories_table.setItem(i, 1, QTableWidgetItem(str(category)))
        
        # 显示样本信息
        self.display_samples(info)
    
    def display_samples(self, info: Dict):
        """显示样本信息"""
        # 清空之前的样本
        for i in reversed(range(self.samples_layout.count())):
            self.samples_layout.itemAt(i).widget().setParent(None)
        
        dataset_type = info.get("type", "")
        
        if dataset_type == "COCO":
            sample_images = info.get("sample_images", [])
            for img_info in sample_images:
                sample_text = f"图片: {img_info.get('file_name', 'Unknown')}\n"
                sample_text += f"尺寸: {img_info.get('width', 0)} x {img_info.get('height', 0)}\n"
                sample_text += f"ID: {img_info.get('id', 0)}"
                
                sample_label = QLabel(sample_text)
                sample_label.setStyleSheet("border: 1px solid #ccc; padding: 5px; margin: 2px;")
                self.samples_layout.addWidget(sample_label)
        
        elif dataset_type in ["YOLO", "Pascal VOC"]:
            sample_key = "sample_images" if dataset_type == "YOLO" else "sample_images"
            samples = info.get(sample_key, [])
            for sample in samples:
                sample_label = QLabel(f"文件: {sample}")
                sample_label.setStyleSheet("border: 1px solid #ccc; padding: 5px; margin: 2px;")
                self.samples_layout.addWidget(sample_label)
        
        elif dataset_type == "LabelMe":
            sample_files = info.get("sample_files", [])
            for sample in sample_files:
                sample_label = QLabel(f"文件: {sample}")
                sample_label.setStyleSheet("border: 1px solid #ccc; padding: 5px; margin: 2px;")
                self.samples_layout.addWidget(sample_label)
        
        self.samples_layout.addStretch()
    
    def display_error(self, error_message: str):
        """显示错误信息"""
        self.info_text.setText(f"错误: {error_message}")
        self.categories_table.setRowCount(0)
        
        # 清空样本
        for i in reversed(range(self.samples_layout.count())):
            self.samples_layout.itemAt(i).widget().setParent(None)
    
    def clear_preview(self):
        """清空预览"""
        self.info_text.setText("请选择数据集文件或目录以预览信息")
        self.categories_table.setRowCount(0)
        
        # 清空样本
        for i in reversed(range(self.samples_layout.count())):
            self.samples_layout.itemAt(i).widget().setParent(None)
    
    def refresh_preview(self):
        """刷新预览"""
        # 这个方法可以由父组件调用来刷新预览
        pass
