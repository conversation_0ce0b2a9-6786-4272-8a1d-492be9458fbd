"""
Progress widget for dataset converter GUI
"""
from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QProgressBar,
    QTextEdit, QLabel, QPushButton, QGroupBox
)
from PySide6.QtCore import Signal, QTimer, Qt
from PySide6.QtGui import QFont


class ProgressWidget(QWidget):
    """进度显示组件"""
    
    cancel_requested = Signal()
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()
        
        # 定时器用于模拟进度更新
        self.timer = QTimer()
        self.timer.timeout.connect(self.update_fake_progress)
        self.fake_progress = 0
    
    def setup_ui(self):
        """设置UI"""
        layout = QVBoxLayout(self)
        
        # 进度组
        progress_group = QGroupBox("转换进度")
        progress_layout = QVBoxLayout(progress_group)
        
        # 状态标签
        self.status_label = QLabel("准备就绪")
        self.status_label.setStyleSheet("font-weight: bold; color: #2196F3;")
        progress_layout.addWidget(self.status_label)
        
        # 进度条
        progress_bar_layout = QHBoxLayout()
        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setValue(0)
        progress_bar_layout.addWidget(self.progress_bar)
        
        # 取消按钮
        self.cancel_button = QPushButton("取消")
        self.cancel_button.setEnabled(False)
        self.cancel_button.clicked.connect(self.cancel_requested.emit)
        progress_bar_layout.addWidget(self.cancel_button)
        
        progress_layout.addLayout(progress_bar_layout)
        
        # 百分比标签
        self.percentage_label = QLabel("0%")
        self.percentage_label.setAlignment(Qt.AlignCenter)
        progress_layout.addWidget(self.percentage_label)
        
        layout.addWidget(progress_group)
        
        # 日志组
        log_group = QGroupBox("转换日志")
        log_layout = QVBoxLayout(log_group)
        
        # 日志文本框
        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        self.log_text.setMaximumHeight(200)
        
        # 设置等宽字体
        font = QFont("Consolas", 9)
        if not font.exactMatch():
            font = QFont("Courier New", 9)
        self.log_text.setFont(font)
        
        log_layout.addWidget(self.log_text)
        
        # 日志控制按钮
        log_buttons_layout = QHBoxLayout()
        
        self.clear_log_button = QPushButton("清空日志")
        self.clear_log_button.clicked.connect(self.clear_log)
        log_buttons_layout.addWidget(self.clear_log_button)
        
        self.save_log_button = QPushButton("保存日志")
        self.save_log_button.clicked.connect(self.save_log)
        log_buttons_layout.addWidget(self.save_log_button)
        
        log_buttons_layout.addStretch()
        log_layout.addLayout(log_buttons_layout)
        
        layout.addWidget(log_group)
    
    def start_conversion(self):
        """开始转换"""
        self.progress_bar.setValue(0)
        self.percentage_label.setText("0%")
        self.status_label.setText("转换中...")
        self.status_label.setStyleSheet("font-weight: bold; color: #FF9800;")
        self.cancel_button.setEnabled(True)
        
        # 开始模拟进度（实际应用中应该由转换线程更新）
        self.fake_progress = 0
        self.timer.start(100)  # 每100ms更新一次
    
    def update_fake_progress(self):
        """模拟进度更新"""
        self.fake_progress += 1
        if self.fake_progress <= 100:
            self.update_progress(self.fake_progress)
        else:
            self.timer.stop()
    
    def update_progress(self, value: int):
        """更新进度"""
        self.progress_bar.setValue(value)
        self.percentage_label.setText(f"{value}%")
        
        if value >= 100:
            self.finish_conversion(True, "转换完成！")
    
    def add_log(self, message: str):
        """添加日志"""
        from datetime import datetime
        timestamp = datetime.now().strftime("%H:%M:%S")
        formatted_message = f"[{timestamp}] {message}"
        self.log_text.append(formatted_message)
        
        # 自动滚动到底部
        scrollbar = self.log_text.verticalScrollBar()
        scrollbar.setValue(scrollbar.maximum())
    
    def clear_log(self):
        """清空日志"""
        self.log_text.clear()
    
    def save_log(self):
        """保存日志"""
        from .utils import select_save_file
        file_path = select_save_file(self, "保存日志", "Text Files (*.txt)")
        if file_path:
            try:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(self.log_text.toPlainText())
                self.add_log(f"日志已保存到: {file_path}")
            except Exception as e:
                self.add_log(f"保存日志失败: {str(e)}")
    
    def finish_conversion(self, success: bool, message: str):
        """完成转换"""
        self.timer.stop()
        self.cancel_button.setEnabled(False)
        
        if success:
            self.status_label.setText("转换完成")
            self.status_label.setStyleSheet("font-weight: bold; color: #4CAF50;")
            self.progress_bar.setValue(100)
            self.percentage_label.setText("100%")
        else:
            self.status_label.setText("转换失败")
            self.status_label.setStyleSheet("font-weight: bold; color: #F44336;")
        
        self.add_log(message)
    
    def reset(self):
        """重置状态"""
        self.timer.stop()
        self.progress_bar.setValue(0)
        self.percentage_label.setText("0%")
        self.status_label.setText("准备就绪")
        self.status_label.setStyleSheet("font-weight: bold; color: #2196F3;")
        self.cancel_button.setEnabled(False)
        self.fake_progress = 0



