"""
GUI utility functions
"""
import os
import sys
from pathlib import Path
from typing import Dict, List, Optional, Tuple
from PySide6.QtWidgets import QMessageBox, QFileDialog, QWidget
from PySide6.QtCore import QThread, Signal, QObject
from PySide6.QtGui import QPixmap, QIcon


def show_error_message(parent: QWidget, title: str, message: str):
    """显示错误消息框"""
    msg_box = QMessageBox(parent)
    msg_box.setIcon(QMessageBox.Critical)
    msg_box.setWindowTitle(title)
    msg_box.setText(message)
    msg_box.exec()


def show_info_message(parent: QWidget, title: str, message: str):
    """显示信息消息框"""
    msg_box = QMessageBox(parent)
    msg_box.setIcon(QMessageBox.Information)
    msg_box.setWindowTitle(title)
    msg_box.setText(message)
    msg_box.exec()


def show_warning_message(parent: QWidget, title: str, message: str):
    """显示警告消息框"""
    msg_box = QMessageBox(parent)
    msg_box.setIcon(QMessageBox.Warning)
    msg_box.setWindowTitle(title)
    msg_box.setText(message)
    msg_box.exec()


def select_file(parent: QWidget, title: str, file_filter: str = "All Files (*)") -> str:
    """选择单个文件"""
    file_path, _ = QFileDialog.getOpenFileName(parent, title, "", file_filter)
    return file_path


def select_directory(parent: QWidget, title: str) -> str:
    """选择目录"""
    dir_path = QFileDialog.getExistingDirectory(parent, title)
    return dir_path


def select_save_file(parent: QWidget, title: str, file_filter: str = "All Files (*)") -> str:
    """选择保存文件路径"""
    file_path, _ = QFileDialog.getSaveFileName(parent, title, "", file_filter)
    return file_path


def get_conversion_types() -> Dict[str, Dict]:
    """获取支持的转换类型配置"""
    return {
        "COCO → YOLO": {
            "module": "converters.coco_to_yolo",
            "function": "main",
            "description": "将COCO格式转换为YOLO格式",
            "input_type": "file",
            "input_filter": "JSON Files (*.json)",
            "tasks": ["detection", "segmentation", "pose"],
            "params": {
                "coco_json": {"type": "file", "required": True, "description": "COCO标注文件"},
                "output_dir": {"type": "directory", "required": True, "description": "输出目录"},
                "task": {"type": "choice", "choices": ["detection", "segmentation", "pose"], "default": "detection", "description": "任务类型"},
                "copy_images": {"type": "bool", "default": False, "description": "复制图像文件"},
                "images_dir": {"type": "directory", "required": False, "description": "图像目录（如果不在COCO文件中指定）"}
            }
        },
        "YOLO → COCO": {
            "module": "converters.yolo_to_coco",
            "function": "main",
            "description": "将YOLO格式转换为COCO格式",
            "input_type": "directory",
            "tasks": ["detection", "segmentation", "pose"],
            "params": {
                "yolo_dir": {"type": "directory", "required": True, "description": "YOLO数据集目录"},
                "output_file": {"type": "save_file", "required": True, "description": "输出COCO文件", "filter": "JSON Files (*.json)"},
                "task": {"type": "choice", "choices": ["detection", "segmentation", "pose"], "default": "detection", "description": "任务类型"},
                "dataset_name": {"type": "text", "default": "Converted Dataset", "description": "数据集名称"}
            }
        },
        "Pascal VOC → COCO": {
            "module": "converters.voc_to_coco",
            "function": "main",
            "description": "将Pascal VOC格式转换为COCO格式",
            "input_type": "directory",
            "tasks": ["detection"],
            "params": {
                "voc_dir": {"type": "directory", "required": True, "description": "VOC数据集目录"},
                "output_file": {"type": "save_file", "required": True, "description": "输出COCO文件", "filter": "JSON Files (*.json)"},
                "task": {"type": "choice", "choices": ["detection"], "default": "detection", "description": "任务类型"},
                "dataset_name": {"type": "text", "default": "Converted Dataset", "description": "数据集名称"}
            }
        },
        "COCO → Pascal VOC": {
            "module": "converters.coco_to_voc",
            "function": "main",
            "description": "将COCO格式转换为Pascal VOC格式",
            "input_type": "file",
            "input_filter": "JSON Files (*.json)",
            "tasks": ["detection"],
            "params": {
                "coco_json": {"type": "file", "required": True, "description": "COCO标注文件"},
                "images_dir": {"type": "directory", "required": True, "description": "图像目录"},
                "output_dir": {"type": "directory", "required": True, "description": "输出目录"},
                "task": {"type": "choice", "choices": ["detection"], "default": "detection", "description": "任务类型"},
                "copy_images": {"type": "bool", "default": False, "description": "复制图像文件"}
            }
        },
        "YOLO → Pascal VOC": {
            "module": "converters.yolo_to_voc",
            "function": "main",
            "description": "将YOLO格式转换为Pascal VOC格式",
            "input_type": "directory",
            "tasks": ["detection"],
            "params": {
                "yolo_dir": {"type": "directory", "required": True, "description": "YOLO数据集目录"},
                "output_dir": {"type": "directory", "required": True, "description": "输出目录"},
                "task": {"type": "choice", "choices": ["detection"], "default": "detection", "description": "任务类型"}
            }
        },
        "Pascal VOC → YOLO": {
            "module": "converters.voc_to_yolo",
            "function": "main",
            "description": "将Pascal VOC格式转换为YOLO格式",
            "input_type": "directory",
            "tasks": ["detection"],
            "params": {
                "voc_dir": {"type": "directory", "required": True, "description": "VOC数据集目录"},
                "output_dir": {"type": "directory", "required": True, "description": "输出目录"},
                "task": {"type": "choice", "choices": ["detection"], "default": "detection", "description": "任务类型"}
            }
        },
        "LabelMe → COCO": {
            "module": "converters.labelme_to_coco",
            "function": "main",
            "description": "将LabelMe格式转换为COCO格式",
            "input_type": "directory",
            "tasks": ["detection", "segmentation", "mixed"],
            "params": {
                "labelme_dir": {"type": "directory", "required": True, "description": "LabelMe数据集目录"},
                "output_file": {"type": "save_file", "required": True, "description": "输出COCO文件", "filter": "JSON Files (*.json)"},
                "task": {"type": "choice", "choices": ["detection", "segmentation", "mixed"], "default": "detection", "description": "任务类型"},
                "extract_images": {"type": "bool", "default": False, "description": "从JSON中提取图像"}
            }
        },
        "LabelMe → YOLO": {
            "module": "converters.labelme_to_yolo",
            "function": "main",
            "description": "将LabelMe格式转换为YOLO格式",
            "input_type": "directory",
            "tasks": ["detection"],
            "params": {
                "labelme_dir": {"type": "directory", "required": True, "description": "LabelMe数据集目录"},
                "output_dir": {"type": "directory", "required": True, "description": "输出目录"},
                "task": {"type": "choice", "choices": ["detection"], "default": "detection", "description": "任务类型"}
            }
        },
        "COCO → LabelMe": {
            "module": "converters.coco_to_labelme",
            "function": "main",
            "description": "将COCO格式转换为LabelMe格式",
            "input_type": "file",
            "input_filter": "JSON Files (*.json)",
            "tasks": ["detection", "segmentation"],
            "params": {
                "coco_json": {"type": "file", "required": True, "description": "COCO标注文件"},
                "images_dir": {"type": "directory", "required": True, "description": "图像目录"},
                "output_dir": {"type": "directory", "required": True, "description": "输出目录"},
                "task": {"type": "choice", "choices": ["detection", "segmentation"], "default": "detection", "description": "任务类型"},
                "include_image_data": {"type": "bool", "default": False, "description": "在JSON中包含图像数据"}
            }
        },
        "YOLO → LabelMe": {
            "module": "converters.yolo_to_labelme",
            "function": "main",
            "description": "将YOLO格式转换为LabelMe格式",
            "input_type": "directory",
            "tasks": ["detection"],
            "params": {
                "yolo_dir": {"type": "directory", "required": True, "description": "YOLO数据集目录"},
                "output_dir": {"type": "directory", "required": True, "description": "输出目录"},
                "task": {"type": "choice", "choices": ["detection"], "default": "detection", "description": "任务类型"}
            }
        },
        "ImageNet → YOLO分类": {
            "module": "converters.imagenet_to_yolo_cls",
            "function": "main",
            "description": "将ImageNet格式转换为YOLO分类格式",
            "input_type": "directory",
            "tasks": ["classification"],
            "params": {
                "imagenet_dir": {"type": "directory", "required": True, "description": "ImageNet数据集目录"},
                "output_dir": {"type": "directory", "required": True, "description": "输出目录"},
                "copy_images": {"type": "bool", "default": False, "description": "复制图像文件"},
                "train_ratio": {"type": "float", "default": 0.8, "description": "训练集比例"},
                "val_ratio": {"type": "float", "default": 0.1, "description": "验证集比例"},
                "test_ratio": {"type": "float", "default": 0.1, "description": "测试集比例"}
            }
        },
        "YOLO分类 → ImageNet": {
            "module": "converters.yolo_cls_to_imagenet",
            "function": "main",
            "description": "将YOLO分类格式转换为ImageNet格式",
            "input_type": "directory",
            "tasks": ["classification"],
            "params": {
                "yolo_dir": {"type": "directory", "required": True, "description": "YOLO分类数据集目录"},
                "output_dir": {"type": "directory", "required": True, "description": "输出目录"},
                "copy_images": {"type": "bool", "default": False, "description": "复制图像文件"}
            }
        }
    }


class ConversionWorker(QThread):
    """转换工作线程"""
    progress_updated = Signal(int)
    log_updated = Signal(str)
    finished = Signal(bool, str)  # success, message
    
    def __init__(self, conversion_type: str, params: Dict):
        super().__init__()
        self.conversion_type = conversion_type
        self.params = params
        self.conversion_config = get_conversion_types()[conversion_type]
    
    def run(self):
        """执行转换"""
        try:
            # 动态导入转换模块
            module_name = self.conversion_config["module"]
            function_name = self.conversion_config["function"]
            
            # 导入模块
            import importlib
            module = importlib.import_module(module_name)
            conversion_function = getattr(module, function_name)
            
            # 准备参数
            args = []
            for key, value in self.params.items():
                if value is not None and value != "":
                    args.extend([f"--{key}", str(value)])
            
            self.log_updated.emit(f"开始转换: {self.conversion_type}")
            self.log_updated.emit(f"参数: {' '.join(args)}")
            
            # 模拟进度更新
            self.progress_updated.emit(10)
            
            # 执行转换（这里需要修改原始转换函数以支持进度回调）
            # 暂时直接调用main函数
            import sys
            old_argv = sys.argv
            sys.argv = ["converter"] + args
            
            try:
                conversion_function()
                self.progress_updated.emit(100)
                self.finished.emit(True, "转换完成！")
            except Exception as e:
                self.finished.emit(False, f"转换失败: {str(e)}")
            finally:
                sys.argv = old_argv
                
        except Exception as e:
            self.finished.emit(False, f"转换失败: {str(e)}")
