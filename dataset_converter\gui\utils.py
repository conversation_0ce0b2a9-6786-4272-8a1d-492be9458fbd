"""
GUI utility functions
"""
import os
import sys
from pathlib import Path
from typing import Dict, List, Optional, Tuple
from PySide6.QtWidgets import QMessageBox, QFileDialog, QWidget
from PySide6.QtCore import QThread, Signal, QObject
from PySide6.QtGui import QPixmap, QIcon


def show_error_message(parent: QWidget, title: str, message: str):
    """显示错误消息框"""
    msg_box = QMessageBox(parent)
    msg_box.setIcon(QMessageBox.Critical)
    msg_box.setWindowTitle(title)
    msg_box.setText(message)
    msg_box.exec()


def show_info_message(parent: QWidget, title: str, message: str):
    """显示信息消息框"""
    msg_box = QMessageBox(parent)
    msg_box.setIcon(QMessageBox.Information)
    msg_box.setWindowTitle(title)
    msg_box.setText(message)
    msg_box.exec()


def show_warning_message(parent: QWidget, title: str, message: str):
    """显示警告消息框"""
    msg_box = QMessageBox(parent)
    msg_box.setIcon(QMessageBox.Warning)
    msg_box.setWindowTitle(title)
    msg_box.setText(message)
    msg_box.exec()


def select_file(parent: QWidget, title: str, file_filter: str = "All Files (*)") -> str:
    """选择单个文件"""
    file_path, _ = QFileDialog.getOpenFileName(parent, title, "", file_filter)
    return file_path


def select_directory(parent: QWidget, title: str) -> str:
    """选择目录"""
    dir_path = QFileDialog.getExistingDirectory(parent, title)
    return dir_path


def select_save_file(parent: QWidget, title: str, file_filter: str = "All Files (*)") -> str:
    """选择保存文件路径"""
    file_path, _ = QFileDialog.getSaveFileName(parent, title, "", file_filter)
    return file_path


def get_conversion_types() -> Dict[str, Dict]:
    """获取支持的转换类型配置"""
    return {
        "COCO → YOLO": {
            "module": "converters.coco_to_yolo",
            "function": "main",
            "description": "将COCO格式转换为YOLO格式",
            "input_type": "file",
            "input_filter": "JSON Files (*.json)",
            "tasks": ["detection", "segmentation", "pose"],
            "params": {
                "coco_json": {"type": "file", "required": True, "description": "COCO标注文件"},
                "output_dir": {"type": "directory", "required": True, "description": "输出目录"},
                "task": {"type": "choice", "choices": ["detection", "segmentation", "pose"], "default": "detection", "description": "任务类型"},
                "copy_images": {"type": "bool", "default": False, "description": "复制图像文件"},
                "images_dir": {"type": "directory", "required": False, "description": "图像目录（如果不在COCO文件中指定）"}
            }
        },
        "YOLO → COCO": {
            "module": "converters.yolo_to_coco",
            "function": "main",
            "description": "将YOLO格式转换为COCO格式",
            "input_type": "directory",
            "tasks": ["detection", "segmentation", "pose"],
            "params": {
                "yolo_dir": {"type": "directory", "required": True, "description": "YOLO数据集目录"},
                "images_dir": {"type": "directory", "required": True, "description": "图像目录"},
                "output_file": {"type": "save_file", "required": True, "description": "输出COCO文件", "filter": "JSON Files (*.json)"},
                "task": {"type": "choice", "choices": ["detection", "segmentation", "pose"], "default": "detection", "description": "任务类型"},
                "dataset_name": {"type": "text", "default": "Converted Dataset", "description": "数据集名称"}
            }
        },
        "Pascal VOC → COCO": {
            "module": "converters.voc_to_coco",
            "function": "main",
            "description": "将Pascal VOC格式转换为COCO格式",
            "input_type": "directory",
            "tasks": ["detection"],
            "params": {
                "voc_dir": {"type": "directory", "required": True, "description": "VOC数据集目录"},
                "output_file": {"type": "save_file", "required": True, "description": "输出COCO文件", "filter": "JSON Files (*.json)"},
                "task": {"type": "choice", "choices": ["detection"], "default": "detection", "description": "任务类型"},
                "dataset_name": {"type": "text", "default": "Converted Dataset", "description": "数据集名称"}
            }
        },
        "COCO → Pascal VOC": {
            "module": "converters.coco_to_voc",
            "function": "main",
            "description": "将COCO格式转换为Pascal VOC格式",
            "input_type": "file",
            "input_filter": "JSON Files (*.json)",
            "tasks": ["detection"],
            "params": {
                "coco_json": {"type": "file", "required": True, "description": "COCO标注文件"},
                "images_dir": {"type": "directory", "required": True, "description": "图像目录"},
                "output_dir": {"type": "directory", "required": True, "description": "输出目录"},
                "task": {"type": "choice", "choices": ["detection"], "default": "detection", "description": "任务类型"},
                "copy_images": {"type": "bool", "default": False, "description": "复制图像文件"}
            }
        },
        "YOLO → Pascal VOC": {
            "module": "converters.yolo_to_voc",
            "function": "main",
            "description": "将YOLO格式转换为Pascal VOC格式",
            "input_type": "directory",
            "tasks": ["detection"],
            "params": {
                "yolo_dir": {"type": "directory", "required": True, "description": "YOLO数据集目录"},
                "output_dir": {"type": "directory", "required": True, "description": "输出目录"},
                "task": {"type": "choice", "choices": ["detection"], "default": "detection", "description": "任务类型"}
            }
        },
        "Pascal VOC → YOLO": {
            "module": "converters.voc_to_yolo",
            "function": "main",
            "description": "将Pascal VOC格式转换为YOLO格式",
            "input_type": "directory",
            "tasks": ["detection"],
            "params": {
                "voc_dir": {"type": "directory", "required": True, "description": "VOC数据集目录"},
                "output_dir": {"type": "directory", "required": True, "description": "输出目录"},
                "task": {"type": "choice", "choices": ["detection"], "default": "detection", "description": "任务类型"}
            }
        },
        "LabelMe → COCO": {
            "module": "converters.labelme_to_coco",
            "function": "main",
            "description": "将LabelMe格式转换为COCO格式",
            "input_type": "directory",
            "tasks": ["detection", "segmentation", "mixed"],
            "params": {
                "labelme_dir": {"type": "directory", "required": True, "description": "LabelMe数据集目录"},
                "output_file": {"type": "save_file", "required": True, "description": "输出COCO文件", "filter": "JSON Files (*.json)"},
                "task": {"type": "choice", "choices": ["detection", "segmentation", "mixed"], "default": "detection", "description": "任务类型"},
                "extract_images": {"type": "bool", "default": False, "description": "从JSON中提取图像"}
            }
        },
        "LabelMe → YOLO": {
            "module": "converters.labelme_to_yolo",
            "function": "main",
            "description": "将LabelMe格式转换为YOLO格式",
            "input_type": "directory",
            "tasks": ["detection"],
            "params": {
                "labelme_dir": {"type": "directory", "required": True, "description": "LabelMe数据集目录"},
                "output_dir": {"type": "directory", "required": True, "description": "输出目录"},
                "task": {"type": "choice", "choices": ["detection"], "default": "detection", "description": "任务类型"}
            }
        },
        "COCO → LabelMe": {
            "module": "converters.coco_to_labelme",
            "function": "main",
            "description": "将COCO格式转换为LabelMe格式",
            "input_type": "file",
            "input_filter": "JSON Files (*.json)",
            "tasks": ["detection", "segmentation"],
            "params": {
                "coco_json": {"type": "file", "required": True, "description": "COCO标注文件"},
                "images_dir": {"type": "directory", "required": True, "description": "图像目录"},
                "output_dir": {"type": "directory", "required": True, "description": "输出目录"},
                "task": {"type": "choice", "choices": ["detection", "segmentation"], "default": "detection", "description": "任务类型"},
                "include_image_data": {"type": "bool", "default": False, "description": "在JSON中包含图像数据"}
            }
        },
        "YOLO → LabelMe": {
            "module": "converters.yolo_to_labelme",
            "function": "main",
            "description": "将YOLO格式转换为LabelMe格式",
            "input_type": "directory",
            "tasks": ["detection"],
            "params": {
                "yolo_dir": {"type": "directory", "required": True, "description": "YOLO数据集目录"},
                "output_dir": {"type": "directory", "required": True, "description": "输出目录"},
                "task": {"type": "choice", "choices": ["detection"], "default": "detection", "description": "任务类型"}
            }
        },
        "ImageNet → YOLO分类": {
            "module": "converters.imagenet_to_yolo_cls",
            "function": "main",
            "description": "将ImageNet格式转换为YOLO分类格式",
            "input_type": "directory",
            "tasks": ["classification"],
            "params": {
                "imagenet_dir": {"type": "directory", "required": True, "description": "ImageNet数据集目录"},
                "output_dir": {"type": "directory", "required": True, "description": "输出目录"},
                "copy_images": {"type": "bool", "default": False, "description": "复制图像文件"},
                "train_ratio": {"type": "float", "default": 0.8, "description": "训练集比例"},
                "val_ratio": {"type": "float", "default": 0.1, "description": "验证集比例"},
                "test_ratio": {"type": "float", "default": 0.1, "description": "测试集比例"}
            }
        },
        "YOLO分类 → ImageNet": {
            "module": "converters.yolo_cls_to_imagenet",
            "function": "main",
            "description": "将YOLO分类格式转换为ImageNet格式",
            "input_type": "directory",
            "tasks": ["classification"],
            "params": {
                "yolo_dir": {"type": "directory", "required": True, "description": "YOLO分类数据集目录"},
                "output_dir": {"type": "directory", "required": True, "description": "输出目录"},
                "copy_images": {"type": "bool", "default": False, "description": "复制图像文件"}
            }
        }
    }


class ConversionWorker(QThread):
    """转换工作线程"""
    progress_updated = Signal(int)
    log_updated = Signal(str)
    finished = Signal(bool, str)  # success, message

    def __init__(self, conversion_type: str, params: Dict):
        super().__init__()
        self.conversion_type = conversion_type
        self.params = params
        self.conversion_config = get_conversion_types()[conversion_type]

    def run(self):
        """执行转换"""
        try:
            self.log_updated.emit(f"开始转换: {self.conversion_type}")
            self.log_updated.emit(f"参数: {self.params}")

            self.progress_updated.emit(10)

            # 根据转换类型调用相应的转换函数
            success = self.execute_conversion()

            if success:
                self.progress_updated.emit(100)
                self.finished.emit(True, "转换完成！")
            else:
                self.finished.emit(False, "转换失败")

        except Exception as e:
            self.log_updated.emit(f"转换过程中发生错误: {str(e)}")
            self.finished.emit(False, f"转换失败: {str(e)}")

    def execute_conversion(self) -> bool:
        """执行具体的转换"""
        try:
            if self.conversion_type == "COCO → YOLO":
                return self.convert_coco_to_yolo()
            elif self.conversion_type == "YOLO → COCO":
                return self.convert_yolo_to_coco()
            elif self.conversion_type == "Pascal VOC → COCO":
                return self.convert_voc_to_coco()
            elif self.conversion_type == "COCO → Pascal VOC":
                return self.convert_coco_to_voc()
            elif self.conversion_type == "YOLO → Pascal VOC":
                return self.convert_yolo_to_voc()
            elif self.conversion_type == "Pascal VOC → YOLO":
                return self.convert_voc_to_yolo()
            elif self.conversion_type == "LabelMe → COCO":
                return self.convert_labelme_to_coco()
            elif self.conversion_type == "LabelMe → YOLO":
                return self.convert_labelme_to_yolo()
            elif self.conversion_type == "COCO → LabelMe":
                return self.convert_coco_to_labelme()
            elif self.conversion_type == "YOLO → LabelMe":
                return self.convert_yolo_to_labelme()
            elif self.conversion_type == "ImageNet → YOLO分类":
                return self.convert_imagenet_to_yolo_cls()
            elif self.conversion_type == "YOLO分类 → ImageNet":
                return self.convert_yolo_cls_to_imagenet()
            else:
                self.log_updated.emit(f"不支持的转换类型: {self.conversion_type}")
                return False
        except Exception as e:
            self.log_updated.emit(f"转换执行失败: {str(e)}")
            return False

    def convert_coco_to_yolo(self) -> bool:
        """COCO转YOLO"""
        try:
            from converters.coco_to_yolo import (
                convert_coco_to_yolo_detection,
                convert_coco_to_yolo_segmentation,
                convert_coco_to_yolo_pose
            )
            from utils.coco_utils import COCODataset

            coco_json = self.params.get('coco_json')
            output_dir = self.params.get('output_dir')
            task = self.params.get('task', 'detection')
            copy_images = self.params.get('copy_images', False)

            if not coco_json or not output_dir:
                self.log_updated.emit("缺少必要参数: coco_json 或 output_dir")
                return False

            self.log_updated.emit(f"加载COCO数据集: {coco_json}")
            self.progress_updated.emit(20)

            coco_dataset = COCODataset(coco_json)
            self.progress_updated.emit(40)

            self.log_updated.emit(f"开始转换，任务类型: {task}")

            if task == 'detection':
                convert_coco_to_yolo_detection(coco_dataset, output_dir, copy_images)
            elif task == 'segmentation':
                convert_coco_to_yolo_segmentation(coco_dataset, output_dir, copy_images)
            elif task == 'pose':
                convert_coco_to_yolo_pose(coco_dataset, output_dir, copy_images)
            else:
                self.log_updated.emit(f"不支持的任务类型: {task}")
                return False

            self.progress_updated.emit(90)
            self.log_updated.emit(f"转换完成，输出目录: {output_dir}")
            return True

        except Exception as e:
            self.log_updated.emit(f"COCO转YOLO失败: {str(e)}")
            return False

    def convert_yolo_to_coco(self) -> bool:
        """YOLO转COCO"""
        try:
            from converters.yolo_to_coco import (
                convert_yolo_to_coco_detection,
                convert_yolo_to_coco_segmentation,
                convert_yolo_to_coco_pose
            )

            yolo_dir = self.params.get('yolo_dir')
            images_dir = self.params.get('images_dir')
            output_file = self.params.get('output_file')
            task = self.params.get('task', 'detection')

            if not yolo_dir or not images_dir or not output_file:
                self.log_updated.emit("缺少必要参数: yolo_dir, images_dir 或 output_file")
                return False

            self.log_updated.emit(f"加载YOLO数据集: {yolo_dir}")
            self.log_updated.emit(f"图像目录: {images_dir}")
            self.progress_updated.emit(20)

            self.log_updated.emit(f"开始转换，任务类型: {task}")
            self.progress_updated.emit(40)

            if task == 'detection':
                convert_yolo_to_coco_detection(yolo_dir, images_dir, output_file)
            elif task == 'segmentation':
                convert_yolo_to_coco_segmentation(yolo_dir, images_dir, output_file)
            elif task == 'pose':
                convert_yolo_to_coco_pose(yolo_dir, images_dir, output_file)
            else:
                self.log_updated.emit(f"不支持的任务类型: {task}")
                return False

            self.progress_updated.emit(90)
            self.log_updated.emit(f"转换完成，输出文件: {output_file}")
            return True

        except Exception as e:
            self.log_updated.emit(f"YOLO转COCO失败: {str(e)}")
            return False

    def convert_voc_to_coco(self) -> bool:
        """VOC转COCO"""
        try:
            from converters.voc_to_coco import convert_voc_to_coco

            voc_dir = self.params.get('voc_dir')
            output_file = self.params.get('output_file')
            task = self.params.get('task', 'detection')

            if not voc_dir or not output_file:
                self.log_updated.emit("缺少必要参数: voc_dir 或 output_file")
                return False

            self.log_updated.emit(f"加载VOC数据集: {voc_dir}")
            self.progress_updated.emit(20)

            self.log_updated.emit(f"开始转换VOC到COCO格式，任务类型: {task}")
            self.progress_updated.emit(40)

            convert_voc_to_coco(voc_dir, output_file, task)

            self.progress_updated.emit(90)
            self.log_updated.emit(f"转换完成，输出文件: {output_file}")
            return True

        except Exception as e:
            self.log_updated.emit(f"VOC转COCO失败: {str(e)}")
            return False

    def convert_coco_to_voc(self) -> bool:
        """COCO转VOC"""
        try:
            from converters.coco_to_voc import convert_coco_to_voc_detection
            from utils.coco_utils import COCODataset

            coco_json = self.params.get('coco_json')
            images_dir = self.params.get('images_dir')
            output_dir = self.params.get('output_dir')
            copy_images = self.params.get('copy_images', False)

            if not coco_json or not output_dir:
                self.log_updated.emit("缺少必要参数: coco_json 或 output_dir")
                return False

            self.log_updated.emit(f"加载COCO数据集: {coco_json}")
            self.progress_updated.emit(20)

            coco_dataset = COCODataset(coco_json)
            self.progress_updated.emit(40)

            self.log_updated.emit("开始转换COCO到VOC格式")

            convert_coco_to_voc_detection(coco_dataset, images_dir, output_dir, copy_images)

            self.progress_updated.emit(90)
            self.log_updated.emit(f"转换完成，输出目录: {output_dir}")
            return True

        except Exception as e:
            self.log_updated.emit(f"COCO转VOC失败: {str(e)}")
            return False

    def convert_yolo_to_voc(self) -> bool:
        """YOLO转VOC"""
        try:
            from converters.yolo_to_voc import convert_yolo_to_voc_detection

            yolo_dir = self.params.get('yolo_dir')
            output_dir = self.params.get('output_dir')

            if not yolo_dir or not output_dir:
                self.log_updated.emit("缺少必要参数: yolo_dir 或 output_dir")
                return False

            self.log_updated.emit(f"加载YOLO数据集: {yolo_dir}")
            self.progress_updated.emit(20)

            self.log_updated.emit("开始转换YOLO到VOC格式")
            self.progress_updated.emit(40)

            convert_yolo_to_voc_detection(yolo_dir, output_dir)

            self.progress_updated.emit(90)
            self.log_updated.emit(f"转换完成，输出目录: {output_dir}")
            return True

        except Exception as e:
            self.log_updated.emit(f"YOLO转VOC失败: {str(e)}")
            return False

    def convert_voc_to_yolo(self) -> bool:
        """VOC转YOLO"""
        try:
            from converters.voc_to_yolo import convert_voc_to_yolo_detection

            voc_dir = self.params.get('voc_dir')
            output_dir = self.params.get('output_dir')

            if not voc_dir or not output_dir:
                self.log_updated.emit("缺少必要参数: voc_dir 或 output_dir")
                return False

            self.log_updated.emit(f"加载VOC数据集: {voc_dir}")
            self.progress_updated.emit(20)

            self.log_updated.emit("开始转换VOC到YOLO格式")
            self.progress_updated.emit(40)

            convert_voc_to_yolo_detection(voc_dir, output_dir)

            self.progress_updated.emit(90)
            self.log_updated.emit(f"转换完成，输出目录: {output_dir}")
            return True

        except Exception as e:
            self.log_updated.emit(f"VOC转YOLO失败: {str(e)}")
            return False

    def convert_labelme_to_coco(self) -> bool:
        """LabelMe转COCO"""
        try:
            from converters.labelme_to_coco import (
                convert_labelme_to_coco_detection,
                convert_labelme_to_coco_segmentation
            )

            labelme_dir = self.params.get('labelme_dir')
            output_file = self.params.get('output_file')
            task = self.params.get('task', 'detection')
            extract_images = self.params.get('extract_images', False)

            if not labelme_dir or not output_file:
                self.log_updated.emit("缺少必要参数: labelme_dir 或 output_file")
                return False

            self.log_updated.emit(f"加载LabelMe数据集: {labelme_dir}")
            self.progress_updated.emit(20)

            self.log_updated.emit(f"开始转换，任务类型: {task}")
            self.progress_updated.emit(40)

            if task == 'detection':
                convert_labelme_to_coco_detection(labelme_dir, output_file, extract_images)
            elif task == 'segmentation':
                convert_labelme_to_coco_segmentation(labelme_dir, output_file, extract_images)
            elif task == 'mixed':
                # 混合模式，同时支持检测和分割
                convert_labelme_to_coco_segmentation(labelme_dir, output_file, extract_images)
            else:
                self.log_updated.emit(f"不支持的任务类型: {task}")
                return False

            self.progress_updated.emit(90)
            self.log_updated.emit(f"转换完成，输出文件: {output_file}")
            return True

        except Exception as e:
            self.log_updated.emit(f"LabelMe转COCO失败: {str(e)}")
            return False

    def convert_labelme_to_yolo(self) -> bool:
        """LabelMe转YOLO"""
        try:
            from converters.labelme_to_yolo import convert_labelme_to_yolo_detection

            labelme_dir = self.params.get('labelme_dir')
            output_dir = self.params.get('output_dir')

            if not labelme_dir or not output_dir:
                self.log_updated.emit("缺少必要参数: labelme_dir 或 output_dir")
                return False

            self.log_updated.emit(f"加载LabelMe数据集: {labelme_dir}")
            self.progress_updated.emit(20)

            self.log_updated.emit("开始转换LabelMe到YOLO格式")
            self.progress_updated.emit(40)

            convert_labelme_to_yolo_detection(labelme_dir, output_dir)

            self.progress_updated.emit(90)
            self.log_updated.emit(f"转换完成，输出目录: {output_dir}")
            return True

        except Exception as e:
            self.log_updated.emit(f"LabelMe转YOLO失败: {str(e)}")
            return False

    def convert_coco_to_labelme(self) -> bool:
        """COCO转LabelMe"""
        try:
            from converters.coco_to_labelme import (
                convert_coco_to_labelme_detection,
                convert_coco_to_labelme_segmentation
            )
            from utils.coco_utils import COCODataset

            coco_json = self.params.get('coco_json')
            images_dir = self.params.get('images_dir')
            output_dir = self.params.get('output_dir')
            task = self.params.get('task', 'detection')
            include_image_data = self.params.get('include_image_data', False)

            if not coco_json or not output_dir:
                self.log_updated.emit("缺少必要参数: coco_json 或 output_dir")
                return False

            self.log_updated.emit(f"加载COCO数据集: {coco_json}")
            self.progress_updated.emit(20)

            coco_dataset = COCODataset(coco_json)
            self.progress_updated.emit(40)

            self.log_updated.emit(f"开始转换，任务类型: {task}")

            if task == 'detection':
                convert_coco_to_labelme_detection(coco_dataset, images_dir, output_dir, include_image_data)
            elif task == 'segmentation':
                convert_coco_to_labelme_segmentation(coco_dataset, images_dir, output_dir, include_image_data)
            else:
                self.log_updated.emit(f"不支持的任务类型: {task}")
                return False

            self.progress_updated.emit(90)
            self.log_updated.emit(f"转换完成，输出目录: {output_dir}")
            return True

        except Exception as e:
            self.log_updated.emit(f"COCO转LabelMe失败: {str(e)}")
            return False

    def convert_yolo_to_labelme(self) -> bool:
        """YOLO转LabelMe"""
        try:
            from converters.yolo_to_labelme import convert_yolo_to_labelme_detection

            yolo_dir = self.params.get('yolo_dir')
            output_dir = self.params.get('output_dir')

            if not yolo_dir or not output_dir:
                self.log_updated.emit("缺少必要参数: yolo_dir 或 output_dir")
                return False

            self.log_updated.emit(f"加载YOLO数据集: {yolo_dir}")
            self.progress_updated.emit(20)

            self.log_updated.emit("开始转换YOLO到LabelMe格式")
            self.progress_updated.emit(40)

            convert_yolo_to_labelme_detection(yolo_dir, output_dir)

            self.progress_updated.emit(90)
            self.log_updated.emit(f"转换完成，输出目录: {output_dir}")
            return True

        except Exception as e:
            self.log_updated.emit(f"YOLO转LabelMe失败: {str(e)}")
            return False

    def convert_imagenet_to_yolo_cls(self) -> bool:
        """ImageNet转YOLO分类"""
        try:
            from converters.imagenet_to_yolo_cls import convert_imagenet_to_yolo_classification

            imagenet_dir = self.params.get('imagenet_dir')
            output_dir = self.params.get('output_dir')
            copy_images = self.params.get('copy_images', False)
            train_ratio = self.params.get('train_ratio', 0.8)
            val_ratio = self.params.get('val_ratio', 0.1)
            test_ratio = self.params.get('test_ratio', 0.1)

            if not imagenet_dir or not output_dir:
                self.log_updated.emit("缺少必要参数: imagenet_dir 或 output_dir")
                return False

            self.log_updated.emit(f"加载ImageNet数据集: {imagenet_dir}")
            self.progress_updated.emit(20)

            self.log_updated.emit("开始转换ImageNet到YOLO分类格式")
            self.progress_updated.emit(40)

            convert_imagenet_to_yolo_classification(
                imagenet_dir, output_dir, copy_images,
                train_ratio, val_ratio, test_ratio
            )

            self.progress_updated.emit(90)
            self.log_updated.emit(f"转换完成，输出目录: {output_dir}")
            return True

        except Exception as e:
            self.log_updated.emit(f"ImageNet转YOLO分类失败: {str(e)}")
            return False

    def convert_yolo_cls_to_imagenet(self) -> bool:
        """YOLO分类转ImageNet"""
        try:
            from converters.yolo_cls_to_imagenet import convert_yolo_cls_to_imagenet

            yolo_dir = self.params.get('yolo_dir')
            output_dir = self.params.get('output_dir')
            copy_images = self.params.get('copy_images', False)

            if not yolo_dir or not output_dir:
                self.log_updated.emit("缺少必要参数: yolo_dir 或 output_dir")
                return False

            self.log_updated.emit(f"加载YOLO分类数据集: {yolo_dir}")
            self.progress_updated.emit(20)

            self.log_updated.emit("开始转换YOLO分类到ImageNet格式")
            self.progress_updated.emit(40)

            convert_yolo_cls_to_imagenet(yolo_dir, output_dir, copy_images)

            self.progress_updated.emit(90)
            self.log_updated.emit(f"转换完成，输出目录: {output_dir}")
            return True

        except Exception as e:
            self.log_updated.emit(f"YOLO分类转ImageNet失败: {str(e)}")
            return False
