#!/usr/bin/env python3
"""
GUI main entry point for dataset converter
"""
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from PySide6.QtWidgets import QApplication, QMessageBox
from PySide6.QtCore import Qt
from PySide6.QtGui import QIcon

from gui.main_window import MainWindow


def check_dependencies():
    """检查依赖是否安装"""
    missing_deps = []
    
    try:
        import cv2
    except ImportError:
        missing_deps.append("opencv-python")
    
    try:
        import PIL
    except ImportError:
        missing_deps.append("Pillow")
    
    try:
        import numpy
    except ImportError:
        missing_deps.append("numpy")
    
    try:
        import pycocotools
    except ImportError:
        missing_deps.append("pycocotools")
    
    try:
        import lxml
    except ImportError:
        missing_deps.append("lxml")
    
    try:
        import tqdm
    except ImportError:
        missing_deps.append("tqdm")
    
    try:
        import matplotlib
    except ImportError:
        missing_deps.append("matplotlib")
    
    try:
        import skimage
    except ImportError:
        missing_deps.append("scikit-image")
    
    try:
        import yaml
    except ImportError:
        missing_deps.append("PyYAML")
    
    return missing_deps


def show_dependency_error(missing_deps):
    """显示依赖错误信息"""
    app = QApplication(sys.argv)
    
    error_msg = "以下依赖包未安装或版本不兼容：\n\n"
    for dep in missing_deps:
        error_msg += f"• {dep}\n"
    
    error_msg += "\n请运行以下命令安装依赖：\n"
    error_msg += "pip install -r requirements.txt\n\n"
    error_msg += "或者使用conda：\n"
    error_msg += "conda env create -f environment.yml"
    
    msg_box = QMessageBox()
    msg_box.setIcon(QMessageBox.Critical)
    msg_box.setWindowTitle("依赖错误")
    msg_box.setText("缺少必要的依赖包")
    msg_box.setDetailedText(error_msg)
    msg_box.exec()
    
    sys.exit(1)


def setup_application():
    """设置应用程序"""
    app = QApplication(sys.argv)
    
    # 设置应用程序信息
    app.setApplicationName("数据集转换器")
    app.setApplicationDisplayName("Dataset Converter")
    app.setApplicationVersion("1.0.0")
    app.setOrganizationName("Dataset Converter Team")
    
    # 设置应用程序图标（如果有的话）
    icon_path = project_root / "assets" / "icon.png"
    if icon_path.exists():
        app.setWindowIcon(QIcon(str(icon_path)))
    
    # 设置样式
    app.setStyle("Fusion")  # 使用Fusion样式，在所有平台上都有一致的外观
    
    # 设置高DPI支持
    app.setAttribute(Qt.AA_EnableHighDpiScaling, True)
    app.setAttribute(Qt.AA_UseHighDpiPixmaps, True)
    
    return app


def apply_dark_theme(app):
    """应用深色主题（可选）"""
    dark_stylesheet = """
    QMainWindow {
        background-color: #2b2b2b;
        color: #ffffff;
    }
    
    QWidget {
        background-color: #2b2b2b;
        color: #ffffff;
    }
    
    QGroupBox {
        font-weight: bold;
        border: 2px solid #555555;
        border-radius: 5px;
        margin-top: 1ex;
        padding-top: 10px;
    }
    
    QGroupBox::title {
        subcontrol-origin: margin;
        left: 10px;
        padding: 0 5px 0 5px;
    }
    
    QPushButton {
        background-color: #404040;
        border: 1px solid #555555;
        padding: 5px;
        border-radius: 3px;
    }
    
    QPushButton:hover {
        background-color: #505050;
    }
    
    QPushButton:pressed {
        background-color: #353535;
    }
    
    QLineEdit, QTextEdit, QComboBox {
        background-color: #404040;
        border: 1px solid #555555;
        padding: 3px;
        border-radius: 3px;
    }
    
    QProgressBar {
        border: 1px solid #555555;
        border-radius: 3px;
        text-align: center;
    }
    
    QProgressBar::chunk {
        background-color: #4CAF50;
        border-radius: 3px;
    }
    
    QTabWidget::pane {
        border: 1px solid #555555;
    }
    
    QTabBar::tab {
        background-color: #404040;
        border: 1px solid #555555;
        padding: 5px 10px;
        margin-right: 2px;
    }
    
    QTabBar::tab:selected {
        background-color: #505050;
    }
    
    QTableWidget {
        gridline-color: #555555;
        background-color: #404040;
    }
    
    QHeaderView::section {
        background-color: #505050;
        border: 1px solid #555555;
        padding: 3px;
    }
    
    QMenuBar {
        background-color: #353535;
        border-bottom: 1px solid #555555;
    }
    
    QMenuBar::item {
        background-color: transparent;
        padding: 4px 8px;
    }
    
    QMenuBar::item:selected {
        background-color: #505050;
    }
    
    QMenu {
        background-color: #404040;
        border: 1px solid #555555;
    }
    
    QMenu::item {
        padding: 4px 20px;
    }
    
    QMenu::item:selected {
        background-color: #505050;
    }
    
    QToolBar {
        background-color: #353535;
        border: none;
        spacing: 3px;
    }
    
    QStatusBar {
        background-color: #353535;
        border-top: 1px solid #555555;
    }
    """
    
    app.setStyleSheet(dark_stylesheet)


def main():
    """主函数"""
    # 检查依赖
    missing_deps = check_dependencies()
    if missing_deps:
        show_dependency_error(missing_deps)
        return
    
    # 设置应用程序
    app = setup_application()
    
    # 可选：应用深色主题
    # apply_dark_theme(app)
    
    try:
        # 创建主窗口
        main_window = MainWindow()
        main_window.show()
        
        # 显示欢迎信息
        print("=" * 60)
        print("           数据集转换器 GUI 版本")
        print("    Dataset Converter GUI v1.0")
        print("=" * 60)
        print("GUI界面已启动，请在窗口中进行操作。")
        print("支持的转换格式：COCO, YOLO, Pascal VOC, LabelMe, ImageNet")
        print("=" * 60)
        
        # 运行应用程序
        sys.exit(app.exec())
        
    except Exception as e:
        print(f"启动GUI失败: {str(e)}")
        
        # 显示错误对话框
        error_app = QApplication(sys.argv) if not QApplication.instance() else QApplication.instance()
        msg_box = QMessageBox()
        msg_box.setIcon(QMessageBox.Critical)
        msg_box.setWindowTitle("启动错误")
        msg_box.setText("GUI启动失败")
        msg_box.setDetailedText(f"错误详情：\n{str(e)}")
        msg_box.exec()
        
        sys.exit(1)


if __name__ == "__main__":
    main()
