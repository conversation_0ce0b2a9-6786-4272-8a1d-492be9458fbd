#!/usr/bin/env python3
"""
快速测试脚本 - 验证所有依赖是否正确安装
"""

def test_imports():
    """测试所有必需的模块导入"""
    print("🔍 测试依赖包导入...")
    
    modules = [
        ("numpy", "import numpy as np"),
        ("Pillow", "from PIL import Image"),
        ("OpenCV", "import cv2"),
        ("matplotlib", "import matplotlib.pyplot as plt"),
        ("scikit-image", "import skimage"),
        ("lxml", "import lxml"),
        ("tqdm", "from tqdm import tqdm"),
        ("PyYAML", "import yaml"),
        ("pycocotools", "from pycocotools import mask")
    ]
    
    success_count = 0
    total_count = len(modules)
    
    for name, import_cmd in modules:
        try:
            exec(import_cmd)
            print(f"✅ {name}")
            success_count += 1
        except ImportError as e:
            print(f"❌ {name}: {e}")
        except Exception as e:
            print(f"⚠️  {name}: {e}")
    
    print(f"\n📊 导入结果: {success_count}/{total_count} 成功")
    return success_count == total_count


def test_basic_functionality():
    """测试基本功能"""
    print("\n🧪 测试基本功能...")
    
    try:
        import numpy as np
        arr = np.array([1, 2, 3])
        print(f"✅ NumPy: 创建数组 {arr}")
    except Exception as e:
        print(f"❌ NumPy: {e}")
    
    try:
        from PIL import Image
        import numpy as np
        img = Image.new('RGB', (100, 100), color='red')
        print(f"✅ Pillow: 创建图像 {img.size}")
    except Exception as e:
        print(f"❌ Pillow: {e}")
    
    try:
        import cv2
        print(f"✅ OpenCV: 版本 {cv2.__version__}")
    except Exception as e:
        print(f"❌ OpenCV: {e}")
    
    try:
        import yaml
        data = {'test': 'value'}
        yaml_str = yaml.dump(data)
        print(f"✅ PyYAML: YAML序列化成功")
    except Exception as e:
        print(f"❌ PyYAML: {e}")


def main():
    """主函数"""
    print("🚀 数据集转换器 - 依赖测试")
    print("=" * 50)
    
    # 测试导入
    imports_ok = test_imports()
    
    # 测试基本功能
    test_basic_functionality()
    
    print("\n" + "=" * 50)
    if imports_ok:
        print("🎉 所有依赖安装成功！可以开始使用数据集转换器了。")
        print("\n📝 快速开始:")
        print("   python convert_dataset.py")
        print("\n📚 查看帮助:")
        print("   python convert_dataset.py --help-all")
    else:
        print("❌ 部分依赖安装失败，请检查上面的错误信息。")
    
    print("\n💡 提示: 如果所有测试都通过，您可以删除这个测试文件:")
    print("   del quick_test.py")


if __name__ == "__main__":
    main()
