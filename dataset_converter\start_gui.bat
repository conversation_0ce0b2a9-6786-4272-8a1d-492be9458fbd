@echo off
echo ====================================
echo    数据集转换器 GUI 启动脚本
echo    Dataset Converter GUI Launcher
echo ====================================
echo.

REM 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到Python，请先安装Python 3.9或更高版本
    echo Error: Python not found, please install Python 3.9 or higher
    pause
    exit /b 1
)

echo 正在启动GUI界面...
echo Starting GUI interface...
echo.

REM 启动GUI
python gui_main.py

if errorlevel 1 (
    echo.
    echo 启动失败，请检查依赖是否正确安装
    echo Launch failed, please check if dependencies are properly installed
    echo.
    echo 安装依赖命令 / Install dependencies:
    echo pip install -r requirements.txt
    echo.
    pause
)
