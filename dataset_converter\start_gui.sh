#!/bin/bash

echo "===================================="
echo "   数据集转换器 GUI 启动脚本"
echo "   Dataset Converter GUI Launcher"
echo "===================================="
echo

# 检查Python是否安装
if ! command -v python3 &> /dev/null; then
    if ! command -v python &> /dev/null; then
        echo "错误: 未找到Python，请先安装Python 3.9或更高版本"
        echo "Error: Python not found, please install Python 3.9 or higher"
        exit 1
    else
        PYTHON_CMD="python"
    fi
else
    PYTHON_CMD="python3"
fi

echo "正在启动GUI界面..."
echo "Starting GUI interface..."
echo

# 启动GUI
$PYTHON_CMD gui_main.py

if [ $? -ne 0 ]; then
    echo
    echo "启动失败，请检查依赖是否正确安装"
    echo "Launch failed, please check if dependencies are properly installed"
    echo
    echo "安装依赖命令 / Install dependencies:"
    echo "pip install -r requirements.txt"
    echo
    read -p "按任意键继续... / Press any key to continue..."
fi
