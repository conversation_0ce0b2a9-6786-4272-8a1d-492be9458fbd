#!/usr/bin/env python3
"""
Test script for GUI components
"""
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_imports():
    """测试所有GUI模块的导入"""
    print("测试GUI模块导入...")
    
    try:
        from PySide6.QtWidgets import QApplication
        print("✓ PySide6.QtWidgets 导入成功")
    except ImportError as e:
        print(f"✗ PySide6.QtWidgets 导入失败: {e}")
        return False
    
    try:
        from PySide6.QtCore import Qt, Signal, QThread
        print("✓ PySide6.QtCore 导入成功")
    except ImportError as e:
        print(f"✗ PySide6.QtCore 导入失败: {e}")
        return False
    
    try:
        from PySide6.QtGui import QFont, QIcon
        print("✓ PySide6.QtGui 导入成功")
    except ImportError as e:
        print(f"✗ PySide6.QtGui 导入失败: {e}")
        return False
    
    try:
        from gui.utils import get_conversion_types, ConversionWorker
        print("✓ gui.utils 导入成功")
    except ImportError as e:
        print(f"✗ gui.utils 导入失败: {e}")
        return False
    
    try:
        from gui.file_selector import FileSelector, ParameterWidget, ParametersGroup
        print("✓ gui.file_selector 导入成功")
    except ImportError as e:
        print(f"✗ gui.file_selector 导入失败: {e}")
        return False
    
    try:
        from gui.progress_widget import ProgressWidget
        print("✓ gui.progress_widget 导入成功")
    except ImportError as e:
        print(f"✗ gui.progress_widget 导入失败: {e}")
        return False
    
    try:
        from gui.conversion_widget import ConversionWidget
        print("✓ gui.conversion_widget 导入成功")
    except ImportError as e:
        print(f"✗ gui.conversion_widget 导入失败: {e}")
        return False
    
    try:
        from gui.preview_widget import PreviewWidget
        print("✓ gui.preview_widget 导入成功")
    except ImportError as e:
        print(f"✗ gui.preview_widget 导入失败: {e}")
        return False
    
    try:
        from gui.main_window import MainWindow
        print("✓ gui.main_window 导入成功")
    except ImportError as e:
        print(f"✗ gui.main_window 导入失败: {e}")
        return False
    
    return True


def test_conversion_types():
    """测试转换类型配置"""
    print("\n测试转换类型配置...")
    
    try:
        from gui.utils import get_conversion_types
        conversion_types = get_conversion_types()
        
        print(f"✓ 支持的转换类型数量: {len(conversion_types)}")
        
        for conv_type in conversion_types.keys():
            print(f"  - {conv_type}")
        
        # 测试一个具体的转换类型
        if "COCO → YOLO" in conversion_types:
            coco_to_yolo = conversion_types["COCO → YOLO"]
            params = coco_to_yolo.get("params", {})
            print(f"✓ COCO → YOLO 参数数量: {len(params)}")
        
        return True
        
    except Exception as e:
        print(f"✗ 转换类型配置测试失败: {e}")
        return False


def test_gui_creation():
    """测试GUI组件创建"""
    print("\n测试GUI组件创建...")
    
    try:
        from PySide6.QtWidgets import QApplication
        from gui.main_window import MainWindow
        
        # 创建应用程序实例
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # 创建主窗口
        main_window = MainWindow()
        print("✓ 主窗口创建成功")
        
        # 测试窗口属性
        title = main_window.windowTitle()
        print(f"✓ 窗口标题: {title}")
        
        size = main_window.size()
        print(f"✓ 窗口尺寸: {size.width()} x {size.height()}")
        
        # 不显示窗口，只测试创建
        return True
        
    except Exception as e:
        print(f"✗ GUI组件创建失败: {e}")
        return False


def test_dependencies():
    """测试项目依赖"""
    print("\n测试项目依赖...")
    
    dependencies = [
        ("opencv-python", "cv2"),
        ("Pillow", "PIL"),
        ("numpy", "numpy"),
        ("pycocotools", "pycocotools"),
        ("lxml", "lxml"),
        ("tqdm", "tqdm"),
        ("matplotlib", "matplotlib"),
        ("scikit-image", "skimage"),
        ("PyYAML", "yaml"),
    ]
    
    all_ok = True
    for dep_name, import_name in dependencies:
        try:
            __import__(import_name)
            print(f"✓ {dep_name} 可用")
        except ImportError:
            print(f"✗ {dep_name} 不可用")
            all_ok = False
    
    return all_ok


def main():
    """主测试函数"""
    print("=" * 60)
    print("           数据集转换器 GUI 测试")
    print("=" * 60)
    
    # 测试依赖
    deps_ok = test_dependencies()
    
    # 测试导入
    imports_ok = test_imports()
    
    # 测试转换类型
    types_ok = test_conversion_types()
    
    # 测试GUI创建
    gui_ok = test_gui_creation()
    
    print("\n" + "=" * 60)
    print("测试结果汇总:")
    print(f"  依赖检查: {'✓ 通过' if deps_ok else '✗ 失败'}")
    print(f"  模块导入: {'✓ 通过' if imports_ok else '✗ 失败'}")
    print(f"  转换配置: {'✓ 通过' if types_ok else '✗ 失败'}")
    print(f"  GUI创建: {'✓ 通过' if gui_ok else '✗ 失败'}")
    
    all_tests_passed = deps_ok and imports_ok and types_ok and gui_ok
    
    if all_tests_passed:
        print("\n🎉 所有测试通过！GUI可以正常启动。")
        print("\n启动GUI:")
        print("  Windows: start_gui.bat")
        print("  Linux/macOS: ./start_gui.sh")
        print("  或直接运行: python gui_main.py")
    else:
        print("\n❌ 部分测试失败，请检查上述错误信息。")
        print("\n建议:")
        print("  1. 确保Python版本 >= 3.9")
        print("  2. 安装依赖: pip install -r requirements.txt")
        print("  3. 检查PySide6是否正确安装")
    
    print("=" * 60)
    
    return 0 if all_tests_passed else 1


if __name__ == "__main__":
    sys.exit(main())
