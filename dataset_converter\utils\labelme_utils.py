"""
LabelMe格式工具函数，用于数据集转换
"""
import json
import os
import base64
from pathlib import Path
from typing import Dict, List, Optional, Union, Tuple
import numpy as np
from PIL import Image
import io
from .common import load_json, save_json, get_image_size, polygon_to_bbox


class LabelMeDataset:
    """LabelMe数据集处理器"""
    
    def __init__(self, dataset_path: Optional[str] = None):
        self.dataset_path = Path(dataset_path) if dataset_path else None
        self.classes = set()
    
    def create_annotation(self, image_path: str, shapes: List[Dict], 
                         image_data: Optional[str] = None) -> Dict:
        """创建LabelMe标注文件
        
        Args:
            image_path: 图像文件路径
            shapes: 标注形状列表
            image_data: 图像的base64编码数据（可选）
        
        Returns:
            LabelMe格式的标注字典
        """
        # 获取图像信息
        if os.path.exists(image_path):
            img_width, img_height = get_image_size(image_path)
            
            # 如果需要，编码图像数据
            if image_data is None:
                try:
                    with open(image_path, 'rb') as f:
                        image_data = base64.b64encode(f.read()).decode('utf-8')
                except:
                    image_data = None
        else:
            img_width, img_height = 0, 0
            image_data = None
        
        annotation = {
            "version": "5.0.1",
            "flags": {},
            "shapes": shapes,
            "imagePath": os.path.basename(image_path),
            "imageData": image_data,
            "imageHeight": img_height,
            "imageWidth": img_width
        }
        
        return annotation
    
    def read_annotation(self, json_file: str) -> Tuple[Dict, List[Dict]]:
        """读取LabelMe标注文件
        
        Args:
            json_file: LabelMe JSON文件路径
            
        Returns:
            (图像信息, 标注列表)
        """
        if not os.path.exists(json_file):
            return {}, []
        
        data = load_json(json_file)
        
        # 提取图像信息
        image_info = {
            'filename': data.get('imagePath', ''),
            'width': data.get('imageWidth', 0),
            'height': data.get('imageHeight', 0),
            'image_data': data.get('imageData')
        }
        
        # 提取标注信息
        annotations = []
        shapes = data.get('shapes', [])
        
        for shape in shapes:
            label = shape.get('label', '')
            shape_type = shape.get('shape_type', '')
            points = shape.get('points', [])
            flags = shape.get('flags', {})
            
            self.classes.add(label)
            
            annotation = {
                'label': label,
                'shape_type': shape_type,
                'points': points,
                'flags': flags
            }
            
            # 根据形状类型处理点坐标
            if shape_type == 'polygon':
                # 多边形：点列表 [[x1,y1], [x2,y2], ...]
                polygon = []
                for point in points:
                    polygon.extend([float(point[0]), float(point[1])])
                annotation['polygon'] = polygon
                annotation['bbox'] = polygon_to_bbox(polygon)
                annotation['type'] = 'segmentation'
                
            elif shape_type == 'rectangle':
                # 矩形：两个对角点 [[x1,y1], [x2,y2]]
                if len(points) >= 2:
                    x1, y1 = float(points[0][0]), float(points[0][1])
                    x2, y2 = float(points[1][0]), float(points[1][1])
                    
                    # 确保坐标顺序正确
                    xmin, xmax = min(x1, x2), max(x1, x2)
                    ymin, ymax = min(y1, y2), max(y1, y2)
                    
                    annotation['bbox'] = [xmin, ymin, xmax - xmin, ymax - ymin]
                    annotation['type'] = 'detection'
                    
            elif shape_type == 'circle':
                # 圆形：中心点和边界点 [[cx,cy], [x,y]]
                if len(points) >= 2:
                    cx, cy = float(points[0][0]), float(points[0][1])
                    x, y = float(points[1][0]), float(points[1][1])
                    
                    # 计算半径
                    radius = ((x - cx) ** 2 + (y - cy) ** 2) ** 0.5
                    
                    # 转换为边界框
                    xmin = cx - radius
                    ymin = cy - radius
                    width = height = 2 * radius
                    
                    annotation['bbox'] = [xmin, ymin, width, height]
                    annotation['center'] = [cx, cy]
                    annotation['radius'] = radius
                    annotation['type'] = 'detection'
                    
            elif shape_type == 'point':
                # 点：单个点 [[x,y]]
                if len(points) >= 1:
                    x, y = float(points[0][0]), float(points[0][1])
                    annotation['point'] = [x, y]
                    annotation['type'] = 'keypoint'
                    
            elif shape_type == 'line' or shape_type == 'linestrip':
                # 线：点列表 [[x1,y1], [x2,y2], ...]
                line_points = []
                for point in points:
                    line_points.extend([float(point[0]), float(point[1])])
                annotation['line'] = line_points
                annotation['type'] = 'line'
            
            annotations.append(annotation)
        
        return image_info, annotations
    
    def save_annotation(self, annotation: Dict, output_file: str) -> None:
        """保存LabelMe标注文件"""
        save_json(annotation, output_file, indent=2)
    
    def get_classes(self) -> List[str]:
        """获取所有类别名称"""
        return sorted(list(self.classes))


def create_labelme_shape(label: str, shape_type: str, points: List[List[float]], 
                        flags: Optional[Dict] = None) -> Dict:
    """创建LabelMe形状对象
    
    Args:
        label: 标签名称
        shape_type: 形状类型 ('polygon', 'rectangle', 'circle', 'point', 'line')
        points: 点坐标列表
        flags: 标志字典（可选）
    
    Returns:
        LabelMe形状字典
    """
    if flags is None:
        flags = {}
    
    shape = {
        "label": label,
        "points": points,
        "group_id": None,
        "shape_type": shape_type,
        "flags": flags
    }
    
    return shape


def polygon_to_labelme_shape(label: str, polygon: List[float]) -> Dict:
    """将多边形坐标转换为LabelMe形状
    
    Args:
        label: 标签名称
        polygon: 多边形坐标 [x1, y1, x2, y2, ...]
    
    Returns:
        LabelMe多边形形状
    """
    points = []
    for i in range(0, len(polygon), 2):
        if i + 1 < len(polygon):
            points.append([polygon[i], polygon[i + 1]])
    
    return create_labelme_shape(label, 'polygon', points)


def bbox_to_labelme_shape(label: str, bbox: List[float], shape_type: str = 'rectangle') -> Dict:
    """将边界框转换为LabelMe形状
    
    Args:
        label: 标签名称
        bbox: 边界框 [x, y, width, height] 或 [xmin, ymin, xmax, ymax]
        shape_type: 形状类型，'rectangle' 或 'polygon'
    
    Returns:
        LabelMe形状
    """
    if len(bbox) == 4:
        if shape_type == 'rectangle':
            # 假设输入是 [x, y, width, height]
            x, y, w, h = bbox
            points = [[x, y], [x + w, y + h]]
        else:  # polygon
            # 假设输入是 [x, y, width, height]
            x, y, w, h = bbox
            points = [[x, y], [x + w, y], [x + w, y + h], [x, y + h]]
    else:
        raise ValueError("边界框必须包含4个值")
    
    return create_labelme_shape(label, shape_type, points)


def keypoint_to_labelme_shape(label: str, keypoint: List[float]) -> Dict:
    """将关键点转换为LabelMe形状
    
    Args:
        label: 标签名称
        keypoint: 关键点坐标 [x, y]
    
    Returns:
        LabelMe点形状
    """
    if len(keypoint) >= 2:
        points = [[keypoint[0], keypoint[1]]]
        return create_labelme_shape(label, 'point', points)
    else:
        raise ValueError("关键点必须包含x, y坐标")


def convert_coco_polygon_to_labelme(polygon: List[float]) -> List[List[float]]:
    """将COCO多边形格式转换为LabelMe点格式
    
    Args:
        polygon: COCO多边形坐标 [x1, y1, x2, y2, ...]
    
    Returns:
        LabelMe点列表 [[x1, y1], [x2, y2], ...]
    """
    points = []
    for i in range(0, len(polygon), 2):
        if i + 1 < len(polygon):
            points.append([polygon[i], polygon[i + 1]])
    return points


def convert_labelme_points_to_coco(points: List[List[float]]) -> List[float]:
    """将LabelMe点格式转换为COCO多边形格式
    
    Args:
        points: LabelMe点列表 [[x1, y1], [x2, y2], ...]
    
    Returns:
        COCO多边形坐标 [x1, y1, x2, y2, ...]
    """
    polygon = []
    for point in points:
        if len(point) >= 2:
            polygon.extend([point[0], point[1]])
    return polygon


def extract_image_from_labelme(json_file: str, output_dir: str) -> Optional[str]:
    """从LabelMe JSON文件中提取图像数据
    
    Args:
        json_file: LabelMe JSON文件路径
        output_dir: 输出目录
    
    Returns:
        提取的图像文件路径，如果失败返回None
    """
    try:
        data = load_json(json_file)
        image_data = data.get('imageData')
        image_path = data.get('imagePath', '')
        
        if image_data and image_path:
            # 解码base64图像数据
            image_bytes = base64.b64decode(image_data)
            
            # 创建输出路径
            output_path = Path(output_dir) / image_path
            output_path.parent.mkdir(parents=True, exist_ok=True)
            
            # 保存图像
            with open(output_path, 'wb') as f:
                f.write(image_bytes)
            
            return str(output_path)
    except Exception as e:
        print(f"提取图像失败 {json_file}: {e}")
    
    return None


def validate_labelme_annotation(annotation: Dict) -> bool:
    """验证LabelMe标注格式是否正确
    
    Args:
        annotation: LabelMe标注字典
    
    Returns:
        是否有效
    """
    required_fields = ['shapes', 'imagePath', 'imageHeight', 'imageWidth']
    
    for field in required_fields:
        if field not in annotation:
            return False
    
    # 验证形状
    shapes = annotation.get('shapes', [])
    for shape in shapes:
        if not isinstance(shape, dict):
            return False
        
        required_shape_fields = ['label', 'points', 'shape_type']
        for field in required_shape_fields:
            if field not in shape:
                return False
        
        # 验证点格式
        points = shape.get('points', [])
        if not isinstance(points, list):
            return False
        
        for point in points:
            if not isinstance(point, list) or len(point) < 2:
                return False
    
    return True
