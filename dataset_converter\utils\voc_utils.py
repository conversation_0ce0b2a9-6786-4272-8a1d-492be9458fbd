"""
Pascal VOC format utilities for dataset conversion
"""
import os
import xml.etree.ElementTree as ET
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Union
from .common import ensure_dir, get_image_size


class VOCDataset:
    """Pascal VOC dataset handler"""
    
    def __init__(self, dataset_path: Optional[str] = None):
        self.dataset_path = Path(dataset_path) if dataset_path else None
        self.classes = set()
    
    def create_annotation_xml(self, image_info: Dict, annotations: List[Dict], 
                             output_file: str) -> None:
        """Create Pascal VOC XML annotation file"""
        ensure_dir(os.path.dirname(output_file))
        
        # Create root element
        annotation = ET.Element('annotation')
        
        # Add folder
        folder = ET.SubElement(annotation, 'folder')
        folder.text = 'images'
        
        # Add filename
        filename = ET.SubElement(annotation, 'filename')
        filename.text = image_info['filename']
        
        # Add path
        path = ET.SubElement(annotation, 'path')
        path.text = image_info.get('path', image_info['filename'])
        
        # Add source
        source = ET.SubElement(annotation, 'source')
        database = ET.SubElement(source, 'database')
        database.text = 'Unknown'
        
        # Add size
        size = ET.SubElement(annotation, 'size')
        width = ET.SubElement(size, 'width')
        width.text = str(image_info['width'])
        height = ET.SubElement(size, 'height')
        height.text = str(image_info['height'])
        depth = ET.SubElement(size, 'depth')
        depth.text = str(image_info.get('depth', 3))
        
        # Add segmented
        segmented = ET.SubElement(annotation, 'segmented')
        segmented.text = '0'
        
        # Add objects
        for ann in annotations:
            if ann.get('type') in ['detection', 'segmentation']:
                obj = ET.SubElement(annotation, 'object')
                
                # Add name
                name = ET.SubElement(obj, 'name')
                name.text = ann['class_name']
                
                # Add pose
                pose = ET.SubElement(obj, 'pose')
                pose.text = 'Unspecified'
                
                # Add truncated
                truncated = ET.SubElement(obj, 'truncated')
                truncated.text = str(ann.get('truncated', 0))
                
                # Add difficult
                difficult = ET.SubElement(obj, 'difficult')
                difficult.text = str(ann.get('difficult', 0))
                
                # Add bounding box
                bndbox = ET.SubElement(obj, 'bndbox')
                bbox = ann['bbox']  # [xmin, ymin, xmax, ymax]
                
                xmin = ET.SubElement(bndbox, 'xmin')
                xmin.text = str(int(bbox[0]))
                ymin = ET.SubElement(bndbox, 'ymin')
                ymin.text = str(int(bbox[1]))
                xmax = ET.SubElement(bndbox, 'xmax')
                xmax.text = str(int(bbox[2]))
                ymax = ET.SubElement(bndbox, 'ymax')
                ymax.text = str(int(bbox[3]))
                
                # Add segmentation if available
                if ann.get('type') == 'segmentation' and 'polygon' in ann:
                    polygon = ET.SubElement(obj, 'polygon')
                    points = ann['polygon']
                    for i in range(0, len(points), 2):
                        if i + 1 < len(points):
                            point = ET.SubElement(polygon, f'point{i//2 + 1}')
                            point.text = f"{int(points[i])},{int(points[i+1])}"
        
        # Write XML file
        tree = ET.ElementTree(annotation)
        ET.indent(tree, space="  ", level=0)
        tree.write(output_file, encoding='utf-8', xml_declaration=True)
    
    def read_annotation_xml(self, xml_file: str) -> Tuple[Dict, List[Dict]]:
        """Read Pascal VOC XML annotation file"""
        if not os.path.exists(xml_file):
            return {}, []
        
        tree = ET.parse(xml_file)
        root = tree.getroot()
        
        # Get image info
        image_info = {
            'filename': root.find('filename').text if root.find('filename') is not None else '',
            'width': int(root.find('size/width').text) if root.find('size/width') is not None else 0,
            'height': int(root.find('size/height').text) if root.find('size/height') is not None else 0,
            'depth': int(root.find('size/depth').text) if root.find('size/depth') is not None else 3
        }
        
        # Get annotations
        annotations = []
        for obj in root.findall('object'):
            class_name = obj.find('name').text
            self.classes.add(class_name)
            
            # Get bounding box
            bndbox = obj.find('bndbox')
            bbox = [
                float(bndbox.find('xmin').text),
                float(bndbox.find('ymin').text),
                float(bndbox.find('xmax').text),
                float(bndbox.find('ymax').text)
            ]
            
            # Get additional attributes
            truncated = int(obj.find('truncated').text) if obj.find('truncated') is not None else 0
            difficult = int(obj.find('difficult').text) if obj.find('difficult') is not None else 0
            
            annotation = {
                'class_name': class_name,
                'bbox': bbox,  # [xmin, ymin, xmax, ymax]
                'truncated': truncated,
                'difficult': difficult,
                'type': 'detection'
            }
            
            # Check for polygon segmentation
            polygon_elem = obj.find('polygon')
            if polygon_elem is not None:
                polygon = []
                for point_elem in polygon_elem:
                    if point_elem.text:
                        x, y = map(float, point_elem.text.split(','))
                        polygon.extend([x, y])
                if polygon:
                    annotation['polygon'] = polygon
                    annotation['type'] = 'segmentation'
            
            annotations.append(annotation)
        
        return image_info, annotations
    
    def get_classes(self) -> List[str]:
        """Get all unique class names"""
        return sorted(list(self.classes))
    
    def create_imageset_file(self, image_names: List[str], output_file: str) -> None:
        """Create ImageSet file (train.txt, val.txt, etc.)"""
        ensure_dir(os.path.dirname(output_file))
        
        with open(output_file, 'w', encoding='utf-8') as f:
            for image_name in image_names:
                # Remove extension for ImageSet files
                name_without_ext = os.path.splitext(image_name)[0]
                f.write(f"{name_without_ext}\n")


def convert_coco_bbox_to_voc(coco_bbox: List[float]) -> List[float]:
    """Convert COCO bbox [x, y, width, height] to VOC format [xmin, ymin, xmax, ymax]"""
    x, y, w, h = coco_bbox
    return [x, y, x + w, y + h]


def convert_voc_bbox_to_coco(voc_bbox: List[float]) -> List[float]:
    """Convert VOC bbox [xmin, ymin, xmax, ymax] to COCO format [x, y, width, height]"""
    xmin, ymin, xmax, ymax = voc_bbox
    return [xmin, ymin, xmax - xmin, ymax - ymin]


def convert_yolo_bbox_to_voc(yolo_bbox: List[float], img_width: int, img_height: int) -> List[float]:
    """Convert YOLO bbox [x_center, y_center, width, height] normalized to VOC format [xmin, ymin, xmax, ymax]"""
    x_center, y_center, width, height = yolo_bbox
    
    # Convert to pixel coordinates
    x_center *= img_width
    y_center *= img_height
    width *= img_width
    height *= img_height
    
    # Convert to corner format
    xmin = x_center - width / 2
    ymin = y_center - height / 2
    xmax = x_center + width / 2
    ymax = y_center + height / 2
    
    return [xmin, ymin, xmax, ymax]


def convert_voc_bbox_to_yolo(voc_bbox: List[float], img_width: int, img_height: int) -> List[float]:
    """Convert VOC bbox [xmin, ymin, xmax, ymax] to YOLO format [x_center, y_center, width, height] normalized"""
    xmin, ymin, xmax, ymax = voc_bbox
    
    # Calculate center and dimensions
    width = xmax - xmin
    height = ymax - ymin
    x_center = xmin + width / 2
    y_center = ymin + height / 2
    
    # Normalize
    x_center /= img_width
    y_center /= img_height
    width /= img_width
    height /= img_height
    
    return [x_center, y_center, width, height]


def create_voc_directory_structure(output_dir: str) -> Dict[str, str]:
    """Create Pascal VOC directory structure"""
    output_dir = Path(output_dir)
    
    directories = {
        'annotations': output_dir / 'Annotations',
        'images': output_dir / 'JPEGImages',
        'imagesets_main': output_dir / 'ImageSets' / 'Main',
        'imagesets_layout': output_dir / 'ImageSets' / 'Layout',
        'imagesets_segmentation': output_dir / 'ImageSets' / 'Segmentation'
    }
    
    for dir_path in directories.values():
        ensure_dir(dir_path)
    
    return {key: str(path) for key, path in directories.items()}


def split_dataset(image_names: List[str], train_ratio: float = 0.8, 
                 val_ratio: float = 0.1, test_ratio: float = 0.1) -> Dict[str, List[str]]:
    """Split dataset into train/val/test sets"""
    import random
    
    # Ensure ratios sum to 1
    total_ratio = train_ratio + val_ratio + test_ratio
    train_ratio /= total_ratio
    val_ratio /= total_ratio
    test_ratio /= total_ratio
    
    # Shuffle images
    shuffled_images = image_names.copy()
    random.shuffle(shuffled_images)
    
    # Calculate split indices
    total_images = len(shuffled_images)
    train_end = int(total_images * train_ratio)
    val_end = train_end + int(total_images * val_ratio)
    
    return {
        'train': shuffled_images[:train_end],
        'val': shuffled_images[train_end:val_end],
        'test': shuffled_images[val_end:]
    }
