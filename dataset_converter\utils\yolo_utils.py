"""
YOLO format utilities for dataset conversion
"""
import os
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Union
import numpy as np
from .common import (
    ensure_dir, get_image_size, get_image_files, 
    normalize_bbox, denormalize_bbox, center_to_corner, corner_to_center
)


class YOLODataset:
    """YOLO dataset handler"""
    
    def __init__(self, dataset_path: Optional[str] = None):
        self.dataset_path = Path(dataset_path) if dataset_path else None
        self.classes = []
        self.class_to_id = {}
        
        if dataset_path and os.path.exists(dataset_path):
            self.load_classes()
    
    def load_classes(self, classes_file: Optional[str] = None) -> None:
        """Load class names from classes.txt or data.yaml"""
        if classes_file is None and self.dataset_path:
            # Try to find classes file
            classes_file = self.dataset_path / "classes.txt"
            if not classes_file.exists():
                classes_file = self.dataset_path / "data.yaml"
        
        if classes_file and os.path.exists(classes_file):
            if str(classes_file).endswith('.txt'):
                with open(classes_file, 'r', encoding='utf-8') as f:
                    self.classes = [line.strip() for line in f.readlines()]
            elif str(classes_file).endswith('.yaml') or str(classes_file).endswith('.yml'):
                import yaml
                with open(classes_file, 'r', encoding='utf-8') as f:
                    data = yaml.safe_load(f)
                    self.classes = data.get('names', [])
        
        self.class_to_id = {name: idx for idx, name in enumerate(self.classes)}
    
    def save_classes(self, output_file: str) -> None:
        """Save class names to file"""
        with open(output_file, 'w', encoding='utf-8') as f:
            for class_name in self.classes:
                f.write(f"{class_name}\n")
    
    def add_class(self, class_name: str) -> int:
        """Add class and return its ID"""
        if class_name not in self.class_to_id:
            class_id = len(self.classes)
            self.classes.append(class_name)
            self.class_to_id[class_name] = class_id
            return class_id
        return self.class_to_id[class_name]
    
    def get_class_id(self, class_name: str) -> Optional[int]:
        """Get class ID by name"""
        return self.class_to_id.get(class_name)
    
    def get_class_name(self, class_id: int) -> Optional[str]:
        """Get class name by ID"""
        if 0 <= class_id < len(self.classes):
            return self.classes[class_id]
        return None


def read_yolo_detection_annotation(annotation_file: str) -> List[Dict]:
    """Read YOLO detection annotation file"""
    annotations = []
    
    if not os.path.exists(annotation_file):
        return annotations
    
    with open(annotation_file, 'r', encoding='utf-8') as f:
        for line in f:
            line = line.strip()
            if line:
                parts = line.split()
                if len(parts) >= 5:
                    class_id = int(parts[0])
                    x_center = float(parts[1])
                    y_center = float(parts[2])
                    width = float(parts[3])
                    height = float(parts[4])
                    
                    annotations.append({
                        'class_id': class_id,
                        'bbox': [x_center, y_center, width, height],  # normalized center format
                        'type': 'detection'
                    })
    
    return annotations


def write_yolo_detection_annotation(annotations: List[Dict], output_file: str) -> None:
    """Write YOLO detection annotation file"""
    ensure_dir(os.path.dirname(output_file))
    
    with open(output_file, 'w', encoding='utf-8') as f:
        for ann in annotations:
            if ann.get('type') == 'detection':
                class_id = ann['class_id']
                bbox = ann['bbox']  # [x_center, y_center, width, height] normalized
                f.write(f"{class_id} {bbox[0]:.6f} {bbox[1]:.6f} {bbox[2]:.6f} {bbox[3]:.6f}\n")


def read_yolo_segmentation_annotation(annotation_file: str) -> List[Dict]:
    """Read YOLO segmentation annotation file"""
    annotations = []
    
    if not os.path.exists(annotation_file):
        return annotations
    
    with open(annotation_file, 'r', encoding='utf-8') as f:
        for line in f:
            line = line.strip()
            if line:
                parts = line.split()
                if len(parts) >= 7:  # class_id + at least 3 points (6 coordinates)
                    class_id = int(parts[0])
                    polygon = [float(x) for x in parts[1:]]  # normalized coordinates
                    
                    annotations.append({
                        'class_id': class_id,
                        'polygon': polygon,  # [x1, y1, x2, y2, ...]
                        'type': 'segmentation'
                    })
    
    return annotations


def write_yolo_segmentation_annotation(annotations: List[Dict], output_file: str) -> None:
    """Write YOLO segmentation annotation file"""
    ensure_dir(os.path.dirname(output_file))
    
    with open(output_file, 'w', encoding='utf-8') as f:
        for ann in annotations:
            if ann.get('type') == 'segmentation':
                class_id = ann['class_id']
                polygon = ann['polygon']
                polygon_str = ' '.join([f"{coord:.6f}" for coord in polygon])
                f.write(f"{class_id} {polygon_str}\n")


def read_yolo_obb_annotation(annotation_file: str) -> List[Dict]:
    """Read YOLO OBB (Oriented Bounding Box) annotation file"""
    annotations = []
    
    if not os.path.exists(annotation_file):
        return annotations
    
    with open(annotation_file, 'r', encoding='utf-8') as f:
        for line in f:
            line = line.strip()
            if line:
                parts = line.split()
                if len(parts) >= 9:  # class_id + 8 coordinates (4 points)
                    class_id = int(parts[0])
                    obb_points = [float(x) for x in parts[1:9]]  # normalized coordinates
                    
                    annotations.append({
                        'class_id': class_id,
                        'obb': obb_points,  # [x1, y1, x2, y2, x3, y3, x4, y4]
                        'type': 'obb'
                    })
    
    return annotations


def write_yolo_obb_annotation(annotations: List[Dict], output_file: str) -> None:
    """Write YOLO OBB annotation file"""
    ensure_dir(os.path.dirname(output_file))
    
    with open(output_file, 'w', encoding='utf-8') as f:
        for ann in annotations:
            if ann.get('type') == 'obb':
                class_id = ann['class_id']
                obb = ann['obb']
                obb_str = ' '.join([f"{coord:.6f}" for coord in obb])
                f.write(f"{class_id} {obb_str}\n")


def read_yolo_pose_annotation(annotation_file: str) -> List[Dict]:
    """Read YOLO pose/keypoint annotation file"""
    annotations = []
    
    if not os.path.exists(annotation_file):
        return annotations
    
    with open(annotation_file, 'r', encoding='utf-8') as f:
        for line in f:
            line = line.strip()
            if line:
                parts = line.split()
                if len(parts) >= 56:  # class_id + bbox(4) + keypoints(51: 17*3)
                    class_id = int(parts[0])
                    bbox = [float(x) for x in parts[1:5]]  # [x_center, y_center, width, height]
                    keypoints = [float(x) for x in parts[5:]]  # [x1, y1, v1, x2, y2, v2, ...]
                    
                    annotations.append({
                        'class_id': class_id,
                        'bbox': bbox,
                        'keypoints': keypoints,
                        'type': 'pose'
                    })
    
    return annotations


def write_yolo_pose_annotation(annotations: List[Dict], output_file: str) -> None:
    """Write YOLO pose annotation file"""
    ensure_dir(os.path.dirname(output_file))
    
    with open(output_file, 'w', encoding='utf-8') as f:
        for ann in annotations:
            if ann.get('type') == 'pose':
                class_id = ann['class_id']
                bbox = ann['bbox']
                keypoints = ann['keypoints']
                
                bbox_str = ' '.join([f"{coord:.6f}" for coord in bbox])
                keypoints_str = ' '.join([f"{coord:.6f}" for coord in keypoints])
                f.write(f"{class_id} {bbox_str} {keypoints_str}\n")


def convert_coco_bbox_to_yolo(coco_bbox: List[float], img_width: int, img_height: int) -> List[float]:
    """Convert COCO bbox [x, y, width, height] to YOLO format [x_center, y_center, width, height] normalized"""
    x, y, w, h = coco_bbox
    x_center = (x + w / 2) / img_width
    y_center = (y + h / 2) / img_height
    width = w / img_width
    height = h / img_height
    return [x_center, y_center, width, height]


def convert_yolo_bbox_to_coco(yolo_bbox: List[float], img_width: int, img_height: int) -> List[float]:
    """Convert YOLO bbox [x_center, y_center, width, height] normalized to COCO format [x, y, width, height]"""
    x_center, y_center, width, height = yolo_bbox
    w = width * img_width
    h = height * img_height
    x = x_center * img_width - w / 2
    y = y_center * img_height - h / 2
    return [x, y, w, h]


def create_yolo_dataset_yaml(classes: List[str], train_path: str, val_path: str, 
                            test_path: Optional[str] = None) -> Dict:
    """Create YOLO dataset configuration YAML"""
    config = {
        'path': '.',
        'train': train_path,
        'val': val_path,
        'nc': len(classes),
        'names': classes
    }
    
    if test_path:
        config['test'] = test_path
    
    return config
